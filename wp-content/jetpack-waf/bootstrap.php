<?php
define( 'DIS<PERSON><PERSON>_JETPACK_WAF', false );
if ( defined( 'DISABLE_JETPACK_WAF' ) && DISABLE_JETPACK_WAF ) return;
define( 'JETPACK_WAF_MODE', 'silent' );
define( 'JET<PERSON>CK_WAF_SHARE_DATA', false );
define( 'JETPACK_WAF_SHARE_DEBUG_DATA', false );
define( 'JETPACK_WAF_DIR', '/var/www/87a59c83-c58b-4ebc-bf98-1c94bea62d1e/public_html/wp-content/jetpack-waf' );
define( 'JETPACK_WAF_WPCONFIG', '/var/www/87a59c83-c58b-4ebc-bf98-1c94bea62d1e/public_html/wp-content/../wp-config.php' );
define( 'JETPACK_WAF_ENTRYPOINT', 'rules/rules.php' );
require_once '/var/www/87a59c83-c58b-4ebc-bf98-1c94bea62d1e/public_html/wp-content/plugins/jetpack/vendor/autoload.php';
Automattic\Jetpack\Waf\Waf_Runner::initialize();
