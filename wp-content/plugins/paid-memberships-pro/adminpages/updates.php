<?php
	//only admins can get this
	if(!function_exists("current_user_can") || (!current_user_can("manage_options") && !current_user_can("pmpro_updates")))
	{
		die(esc_html__("You do not have permissions to perform this action.", 'paid-memberships-pro' ));
	}

	//reset this transient so we know the page was just loaded
	set_transient('pmpro_updates_first_load', true, 60*60*24);
	
	require_once(dirname(__FILE__) . "/admin_header.php");	
?>

<h2><?php esc_html_e('Updating Paid Memberships Pro', 'paid-memberships-pro' );?></h2>

<?php
	$updates = get_option('pmpro_updates', array());
	if(!empty($updates)) {
		//let's process the first one
	?>
	<p id="pmpro_updates_intro"><?php esc_html_e('Updates are processing. This may take a few minutes to complete.', 'paid-memberships-pro' );?></p>
	<p id="pmpro_updates_progress">[...]</p>
	<textarea id="pmpro_updates_status" rows="10" cols="60"><?php echo esc_textarea( __( 'Loading...', 'paid-memberships-pro' ) ); ?></textarea>

	
	<?php
	} else {
	?><p><?php esc_html_e('Update complete.', 'paid-memberships-pro');?></p><?php
	}
?>

<?php
	require_once(dirname(__FILE__) . "/admin_footer.php");	
?>
