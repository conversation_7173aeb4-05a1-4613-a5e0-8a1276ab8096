<?php

namespace WhichBrowser\Data;

DeviceModels::$ANDROID_INDEX = array (
  '@' => 
  array (
    0 => 'A[0-9]{3,3}!!',
    1 => 'E[0-9]{2,2}0?!!',
    2 => 'S[0-9]{2,2}0?!!',
    3 => 'V[0-9]{2,2}0!!',
    4 => 'Z[0-9]{3,3}!!',
    5 => '[0-9]0[0-9]F!!',
    6 => '(<PERSON><PERSON>[- ])?H[ETW]- ?[A-Z][0-9]!!',
    7 => '(His<PERSON> )?(LED[0-9]{2,2}(G|K|L|EC|XT)[0-9]{2,3})!',
    8 => '[SX06][0-9]{2,2}HT!!',
    9 => '(<PERSON><PERSON><PERSON> )?[MSTX](1|2|7|8|10)\\-[A-Z0-9][0-9]{2,2}!!',
    10 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    11 => 'G[0-9]{3,3}!!',
    12 => 'Y[0-9]{3,3}!!',
    13 => '[0-9AS][0-9]{2,2}HW!!',
    14 => '(USCC-|KYOCERA-)?E[0-9]{4,4}!!',
    15 => '(USCC-|KYOCERA-)?C[0-9]{4,4}!!',
    16 => '[0-9]0[0-9]KC?!!',
    17 => 'K\\-?Touch!!',
    18 => '(Lenovo ?)?(IdeaTab ?)?[AB][0-9]{4,4}!!',
    19 => '(Lenovo )?(Tab ?)?(2 ?)?[AS](7|8|10)!!',
    20 => '(Lenovo ?)?(IdeaTab ?)?[KSV][0-9]{4,4}!!',
    21 => '[0-9]0[0-9]LG!!',
    22 => 'M\\-PP!!',
    23 => 'M\\-MP!!',
    24 => 'M\\-IPRO!!',
    25 => '(MEDION|(MD )?LIFETAB)!!',
    26 => 'M\\.T\\.T\\.!!',
    27 => 'O\\+!!',
    28 => '[0-9]{2,2}PFH6[0-9]{3,3}!',
    29 => '(?:RIVO )?RHYTHM RX ?([0-9]+)!',
    30 => '[4567A]0[0-9]SH!!',
    31 => '(3|5|6|7)0[0-9]SH!!',
    32 => 'C[0-9]{4,4}!!',
    33 => 'D[0-9]{4,4}!!',
    34 => 'E[0-9]{4,4}!!',
    35 => 'F[0-9]{4,4}!!',
    36 => 'G[0-9]{4,4}!!',
    37 => 'H[0-9]{4,4}!!',
    38 => 'I[0-9]{4,4}!!',
    39 => 'J[0-9]{4,4}!!',
    40 => 'E[0-9]{2,2}[a-z]!!',
    41 => 'L[0-9]{2,2}[a-z]!!',
    42 => '(SNM\\-)?M[0-9]{2,2}[a-z]!!',
    43 => 'S[0-9]{2,2}[a-z]!!',
    44 => '(XOLO )?[ABQX][0-9]{3,4}!!',
    45 => '(ZTE ?)?N[0-9]{3,3}!!',
    46 => '(ZXY-)?(ZTE )?N[0-9]{4,4}!!',
    47 => '(ZTE ?)?U[0-9]{3,3}!!',
    48 => '(ZTE ?)?V[0-9]{3,3}[A-Z]!!',
    49 => '(ZTE ?)?V ?[0-9]{3,3}!!',
    50 => '(ZTE ?)?X[0-9]{3,3}!!',
    51 => '(ZTE )?Z[0-9]!!',
    52 => '[a-z][a-z](?:-[a-z][a-z])?, SmartTabII7!',
  ),
  '@00' => 
  array (
    0 => '001DL',
    1 => '003P',
    2 => '003Z',
    3 => '008Z',
    4 => '009Z',
  ),
  '@0P' => 
  array (
    0 => '0PCV1',
    1 => '0PM92',
    2 => '0PJA1!',
    3 => '0PJA2!',
    4 => '0PKV1',
    5 => '0PAJ5',
  ),
  '@10' => 
  array (
    0 => '101DL',
    1 => '101N',
    2 => '101T',
    3 => '101P',
    4 => '102P',
  ),
  '@11' => 
  array (
    0 => 1105,
    1 => 1107,
  ),
  '@12' => 
  array (
    0 => '1216[X]!',
    1 => 1201,
  ),
  '@15' => 
  array (
    0 => '1501-A02',
    1 => '1501 M02',
    2 => '1501-M02',
    3 => '1503-A01',
    4 => '1503-M02',
    5 => '1505-A01',
    6 => '1505-A02',
  ),
  '@16' => 
  array (
    0 => '1605-A01',
    1 => '1607-A01',
  ),
  '@17' => 
  array (
    0 => '1713-A01',
  ),
  '@18' => 
  array (
    0 => '1801-A01',
  ),
  '@2 ' => 
  array (
    0 => '(Lenovo )?(Tab ?)?(2 ?)?[AS](7|8|10)!!',
  ),
  '@20' => 
  array (
    0 => '201M',
    1 => '(Xiaomi )?20!!',
    2 => '(Xiaomi |HM)?20!!',
  ),
  '@21' => 
  array (
    0 => '21061119DG',
    1 => '2107113SG',
    2 => '21081111RG',
    3 => '21091116AG',
    4 => '(Xiaomi |HM)?21!!',
    5 => '21051182G',
  ),
  '@22' => 
  array (
    0 => '2201117TY',
    1 => '2201122G',
    2 => '2201123G',
    3 => '2201116PG',
  ),
  '@2P' => 
  array (
    0 => '2PQ93',
    1 => '2PS64',
  ),
  '@3' => 
  array (
    0 => '(3|5|6|7)0[0-9]SH!!',
  ),
  '@30' => 
  array (
    0 => 3007,
  ),
  '@36' => 
  array (
    0 => '360 N4S',
  ),
  '@3G' => 
  array (
    0 => '3G7334i',
    1 => '(Lenovo |Lephone )?3GC101!',
    2 => '(Lenovo |Lephone )?3GW100!',
    3 => '(Lenovo |Lephone )?3GW101!',
  ),
  '@40' => 
  array (
    0 => '4002[X]!',
    1 => '4003[AJ]!',
    2 => '4007[D]!',
    3 => '4008[A]!',
    4 => '4009[ADEFIKMSX]!',
    5 => '4013[ADEJKMX]!',
    6 => '4014[AMX]!',
    7 => '4015[ANTX]!',
    8 => '4015[D]!',
    9 => '4016[ADX]!',
    10 => '4017[ADEFX]!',
    11 => '4018[ADEFMX]!',
    12 => '4024[DEX]!',
    13 => '4027[ADNX]!',
    14 => '4028[AEJS]!',
    15 => '4032[ADEX]!',
    16 => '4033[ADLX]!',
    17 => '4034[ADEFGX]!',
    18 => '4035[ADYX]!',
    19 => '4036[E]!',
    20 => '4037[AX]!',
    21 => '4037[T]!',
    22 => '4037[V]!',
    23 => '4045[ADEFLOYX]!',
    24 => '4047[ADFGX]!',
    25 => '4049[DG]!',
    26 => '4055[JU]!',
    27 => '4060[A]!',
    28 => '4060[SW]!',
    29 => '40[0-9]SC!!',
    30 => '40[0-9]SO!!',
    31 => '402ZT',
  ),
  '@48' => 
  array (
    0 => '(48|50|55)AX600C!',
  ),
  '@5' => 
  array (
    0 => '(3|5|6|7)0[0-9]SH!!',
  ),
  '@50' => 
  array (
    0 => '5001[D]!',
    1 => '5002[DH]!',
    2 => '5003[D]!',
    3 => '5009[D]!',
    4 => '5010[DEGSUX]!',
    5 => '5011[A]!',
    6 => '5012[G]!',
    7 => '5015[ADEX]!',
    8 => '5016[AJ]!',
    9 => '5017[B]!',
    10 => '5017[ADEOX]!',
    11 => '5019[D]!',
    12 => '5020[A]!',
    13 => '5022[DEX]!',
    14 => '5023[F]!',
    15 => '5024[DF]!',
    16 => '5025[DEGX]!',
    17 => '5027[B]!',
    18 => '5030[DF]!',
    19 => '5033[ADJOX]!',
    20 => '5034[D]!',
    21 => '5036[D]!',
    22 => '5038[ADEX]!',
    23 => '5039[D]!',
    24 => '5041[C]!',
    25 => '5042[ADEFGTWXY]!',
    26 => '5044[ADGIOSTY]!',
    27 => '5045[ADFGJTXY]!',
    28 => '5046[DGIJUY]!',
    29 => '5047[DIUY]!',
    30 => '5048[UY]!',
    31 => '5049[EGSWZ]!',
    32 => '5050[ASYX]!',
    33 => '5051[ADJMX]!',
    34 => '5052[DY]!',
    35 => '5053[DK]!',
    36 => '5054[ADOSTX]!',
    37 => '5054[NW]!',
    38 => '5056[ADEINUWX]!',
    39 => '5056[O]!',
    40 => '5057[M]!',
    41 => '5058[I]!',
    42 => '5059[ADJTXY]!',
    43 => '5060[D]!',
    44 => '5061[KU]!',
    45 => '5065[ADJNWX]!',
    46 => '5070[DJX]!',
    47 => '5080[A]!',
    48 => '5080[DFQX]!',
    49 => '5085[CDJNOYX]!',
    50 => '5086[ADY]!',
    51 => '5090[Y]!',
    52 => '5095[BIKY]!',
    53 => '5096[AI]!',
    54 => '5098[OS]!',
    55 => '5099[ADY]!',
    56 => '504Q',
    57 => '504Q+',
    58 => '504QP',
    59 => '501LV',
    60 => '(48|50|55)AX600C!',
    61 => '5080C Pro',
    62 => '50[0-9]SO!!',
    63 => '502ZT',
  ),
  '@55' => 
  array (
    0 => '5560S',
    1 => '(48|50|55)AX600C!',
  ),
  '@58' => 
  array (
    0 => 5832,
    1 => 5855,
    2 => 5860,
    3 => '5860A',
    4 => '5860E',
    5 => '5860S',
    6 => 5870,
  ),
  '@6' => 
  array (
    0 => '(3|5|6|7)0[0-9]SH!!',
  ),
  '@60' => 
  array (
    0 => '6014[DX]!',
    1 => '6016[ADEX]!',
    2 => '6025[D]!',
    3 => '6031[X]!',
    4 => '6036[AYX]!',
    5 => '6037[BIKY]!',
    6 => '6039[AHJKSY]!',
    7 => '6042[D]!',
    8 => '6043[AD]!',
    9 => '6044D',
    10 => '6045[BFGIKOXY]!',
    11 => '6050[AFWY]!',
    12 => '6055[ABDHIKPUY]!',
    13 => '6056[D]!',
    14 => '6058[D]!',
    15 => '6060[S]!',
    16 => '6062[W]!',
    17 => '6070[KOY]!',
    18 => '602LV',
    19 => '60[0-9]SO!!',
    20 => '602ZT',
    21 => '6034R ORANGE Niva',
  ),
  '@66' => 
  array (
    0 => 6607,
  ),
  '@70' => 
  array (
    0 => '7030[L]!',
    1 => '7040[DNR]!',
    2 => '7040[T]!',
    3 => '7043[AEKY]!',
    4 => '7044[AXY]!',
    5 => '7045[Y]!',
    6 => '7046[T]!',
    7 => '7048[ASWX]!',
    8 => '7050[Y]!',
    9 => '7051[X]!',
    10 => '7053[D]!',
    11 => '7055[AW]!',
    12 => '704DC',
    13 => '70[0-9]SO!!',
  ),
  '@71' => 
  array (
    0 => '710C',
  ),
  '@72' => 
  array (
    0 => 7260,
    1 => 7266,
  ),
  '@75' => 
  array (
    0 => '7500IPS',
  ),
  '@7D' => 
  array (
    0 => '7D-501u',
  ),
  '@80' => 
  array (
    0 => '8020[D]!',
    1 => '8030[BY]!',
    2 => 8063,
    3 => 8082,
    4 => '8082 EEA',
    5 => '8094[X]!',
    6 => 8022,
    7 => '801ES',
    8 => '80-1',
    9 => '801a',
    10 => '801s',
    11 => '802w',
    12 => '800P31C',
  ),
  '@81' => 
  array (
    0 => '8190Q',
  ),
  '@82' => 
  array (
    0 => '825 T-Mobile',
  ),
  '@83' => 
  array (
    0 => '831C',
  ),
  '@86' => 
  array (
    0 => '8681-M02',
    1 => '8692-M02',
  ),
  '@87' => 
  array (
    0 => 8720,
    1 => '8720L',
  ),
  '@88' => 
  array (
    0 => 8810,
  ),
  '@89' => 
  array (
    0 => 8950,
  ),
  '@8X' => 
  array (
    0 => '8x-1000',
  ),
  '@90' => 
  array (
    0 => '9001[DIX]!',
    1 => '9002[AWX]!',
    2 => '9003[AX]!',
    3 => '9005[X]!',
    4 => '9006[W]!',
    5 => '9007[ATX]!',
    6 => '9008[ADIJTUX]!',
    7 => '9010[X]!',
    8 => '9015[BJWQ]!',
    9 => '9022[SX]!',
    10 => '9024[OW]!',
    11 => '9025[MQ]!',
    12 => '9026[X]!',
    13 => '9021[AM]!',
    14 => '909d',
    15 => 9088,
    16 => '9020[A]!',
  ),
  '@93' => 
  array (
    0 => 9300,
    1 => '9300+',
    2 => '932i',
  ),
  '@97' => 
  array (
    0 => '97FC',
  ),
  '@A0' => 
  array (
    0 => 'A001',
    1 => 'A007',
    2 => 'A0001',
    3 => 'A0002',
    4 => 'A05510',
  ),
  '@A1' => 
  array (
    0 => 'A1-[0-9]{3,3}!!',
    1 => 'A10',
    2 => 'A13-?MID!',
    3 => 'A101B',
    4 => 'A101C',
    5 => 'A101B2-LZ',
    6 => 'A101IT',
    7 => 'A10 MID',
    8 => 'A10MID',
    9 => 'A101B-CF',
    10 => 'A1001T',
    11 => '(GIO-)?(GiONEE[- ])?A1$!',
    12 => 'A19S',
    13 => 'A11',
    14 => 'A111',
    15 => 'A106',
    16 => 'A1 07',
    17 => 'A13',
    18 => 'A110Q',
    19 => 'A110Q Canvas 2 Plus',
    20 => 'A110Q Canvas 2+',
    21 => 'A1680',
    22 => 'A1001',
    23 => 'A11w',
    24 => 'A1603',
    25 => 'A1601',
    26 => 'A10H(D5W6)',
    27 => 'A10HD(N9A3)',
    28 => 'A10t(5DM3)',
    29 => 'A10T(8DM1)',
    30 => 'A11(M5A7)',
    31 => 'A11s(M5A7)',
    32 => 'A15(E6C2)',
    33 => 'A11 Pro Max',
  ),
  '@A2' => 
  array (
    0 => 'A206G',
    1 => 'A2',
    2 => 'A21',
  ),
  '@A3' => 
  array (
    0 => 'A3-A[0-9]{2,2}!!',
    1 => 'A32',
    2 => 'A35DE',
    3 => 'A320a',
    4 => 'A3380',
    5 => 'A30t',
    6 => 'A380T',
    7 => 'A31',
    8 => 'A31c',
    9 => 'A31t',
    10 => 'A31u',
    11 => 'A33f',
    12 => 'A33w',
    13 => 'A37f',
    14 => 'A37fw',
    15 => 'A3',
  ),
  '@A4' => 
  array (
    0 => 'A450TL',
    1 => 'A460G',
    2 => 'A460T',
    3 => 'A462C',
    4 => 'A463BG',
    5 => 'A464BG',
    6 => 'A466BG',
    7 => 'A466T',
    8 => 'A480G',
    9 => 'A43',
    10 => 'A400CG',
    11 => 'A45',
    12 => 'A47',
    13 => 'A4',
  ),
  '@A5' => 
  array (
    0 => 'A520L',
    1 => 'A520R',
    2 => 'A521L',
    3 => 'A554C',
    4 => 'A556C',
    5 => 'A560G',
    6 => 'A562G',
    7 => 'A564R',
    8 => 'A564C',
    9 => 'A570BL',
    10 => 'A571VL',
    11 => 'A573VC',
    12 => 'A5 Easy',
    13 => 'A5 Easy TM',
    14 => 'A5 Lite',
    15 => 'A5 Quad Plus',
    16 => 'A5 Quad Plus TM',
    17 => 'A500CG',
    18 => 'A55i',
    19 => 'A52',
    20 => 'A51',
    21 => 'A51f',
    22 => 'A51w',
    23 => 'A5000',
    24 => 'a5',
  ),
  '@A6' => 
  array (
    0 => 'A621BL',
    1 => 'A621R',
    2 => 'A622GL',
    3 => 'A622VL',
    4 => 'A6 Duo',
    5 => 'A6 Lite',
    6 => 'A6277',
    7 => 'A6020',
    8 => 'A6S',
  ),
  '@A7' => 
  array (
    0 => 'A7 Lite',
    1 => 'A726T',
    2 => 'A70HB',
    3 => 'A70BHT',
    4 => 'A70BHT-LZ',
    5 => 'A70BHT-PR',
    6 => 'A70CHT',
    7 => 'A70H',
    8 => 'A70S',
    9 => 'A7EB',
    10 => 'a7272',
    11 => 'A7272+(HTC DesireZ)',
    12 => 'A7 HD',
    13 => 'A7*',
    14 => 'A7+',
    15 => 'A708T',
    16 => 'A750',
    17 => 'A7000-a',
    18 => 'A7010a48',
    19 => 'A73',
    20 => 'A7272+',
    21 => 'A70(CY6T)',
    22 => 'A78h四核(C1V3)',
  ),
  '@A8' => 
  array (
    0 => 'A845L',
    1 => 'A846L',
    2 => 'A851L',
    3 => 'A862W',
    4 => 'A80KSC!',
    5 => 'A80HF',
    6 => 'A80Plus',
    7 => 'A80Pro',
    8 => 'A89',
    9 => 'A820t',
    10 => 'A87',
    11 => 'A853',
    12 => 'A853 Milestone',
    13 => 'A878 Duo',
    14 => 'A898 Duo',
    15 => 'A8HD',
    16 => 'A80h双核(A4P9)',
    17 => 'A80 se??(M5MA)',
    18 => 'A80 se四核(M5MC)',
    19 => 'A80HD四核(C4H6)',
    20 => 'A88 mini四核(M1C5)',
    21 => 'A81E',
  ),
  '@A9' => 
  array (
    0 => 'A995L',
    1 => 'A95',
    2 => 'A97i',
    3 => '(GIO-)?(GiONEE[- ])?A9$!',
    4 => 'A90S',
    5 => 'A953',
    6 => 'A955',
    7 => 'A9 Pro',
  ),
  '@AA' => 
  array (
    0 => 'AA3-600',
  ),
  '@AC' => 
  array (
    0 => 'Acer Chromebook R11!',
    1 => 'Acer Chromebook R13!',
    2 => 'Acer Chromebook 15!',
    3 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
    4 => 'AC45BHE',
    5 => 'AC50BHE',
    6 => 'AC50DHE',
    7 => 'AC45NE',
    8 => 'Action-X3',
    9 => 'ACM3066-8',
    10 => '(Explay|X-tremer|ActiveD|Informer|Surfer)!!',
    11 => 'AC2001',
    12 => 'AC2003',
    13 => 'ACT2000',
    14 => 'ACU Volvo',
    15 => 'Acqua',
  ),
  '@AD' => 
  array (
    0 => 'ADT-1',
    1 => 'ADVAN Q7A',
    2 => 'ADVAN E1C+',
    3 => 'ADVAN S3',
    4 => 'ADVAN S3+',
    5 => 'ADVAN S3A',
    6 => 'ADVAN S3C',
    7 => 'ADVAN S3C 3502',
    8 => 'ADVAN S4',
    9 => 'ADVAN S4+',
    10 => 'ADVAN S4A',
    11 => 'ADVAN S4C',
    12 => 'ADVAN S4E',
    13 => 'Advan S4P [Official]',
    14 => 'ADVAN S5E',
    15 => 'ADVAN S5E Pro',
    16 => 'ADVAN S5F+',
    17 => 'ADVAN S5H',
    18 => 'ADVAN S5I',
    19 => 'Advan S5K',
    20 => 'ADVAN S5M',
    21 => 'ADVAN S5Q',
    22 => 'ADVAN S6A',
    23 => 'ADVAN T1G',
    24 => 'ADVAN T1J',
    25 => 'ADVAN T1J+',
    26 => 'ADVAN T1L',
    27 => 'ADVAN T1M',
    28 => 'ADVAN T1X',
    29 => 'ADVAN T2E',
    30 => 'ADVAN T2F',
    31 => 'ADVAN T3E+',
    32 => 'ADVAN T5C',
    33 => 'Advent Vega',
    34 => 'ADR3010',
    35 => 'AD683G',
    36 => '(HTC|PCD|USCC)?ADR[0-9]{4,4}!!',
    37 => 'Adam',
    38 => 'ADM816KC',
    39 => 'ADM816HC',
    40 => 'ADM712HC',
    41 => 'ADM8000KP A',
    42 => 'ADM8000KP B',
    43 => 'ADR8995!',
    44 => 'ADR910L',
    45 => 'ADR910L 4G',
    46 => 'ADR930L',
    47 => 'ADR930L 4G',
    48 => 'AD686G',
    49 => 'AD687G',
    50 => 'AD6893G',
    51 => 'AD682H',
    52 => 'Admire Alpha',
    53 => 'Admire Curve',
  ),
  '@AE' => 
  array (
    0 => 'Aero A2-110',
    1 => 'Aegis2',
  ),
  '@AF' => 
  array (
    0 => 'AFTB',
    1 => 'AFTS',
    2 => 'AFTN',
    3 => 'AFTRS',
    4 => 'AFTM',
    5 => 'AFTT',
  ),
  '@AG' => 
  array (
    0 => 'AG Chrome Selfie',
    1 => 'AG CHROME ULTRA',
    2 => 'AG Tab 7 0',
    3 => 'AG Go-Tab Access',
    4 => 'AG Chrome Go Tab 7.0',
    5 => 'AGS-(L09|W09)!',
    6 => 'AGS2-(L09|W09)!',
    7 => '(KATBL|Kogan|Agora)!!',
  ),
  '@AI' => 
  array (
    0 => 'aigoPad',
    1 => 'aigoPadM60',
    2 => 'AigoPad M60',
    3 => 'aigoPad M80D',
    4 => 'aigoPad M80E',
    5 => 'Ainovo Aurora-II',
    6 => 'Ainovo Flame (Nexus 7)',
    7 => 'AIRIS GN135',
    8 => 'AIRIS S211',
    9 => 'AIRIS TM([0-9]+[A-Z]*)!',
    10 => 'AIRIS OnePAD 725',
    11 => 'AIRIS OnePAD700',
    12 => 'AIRIS OnePAD 730',
    13 => 'AirisOnePad1000',
    14 => 'AirBook TTJ702',
    15 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
    16 => 'Air!!',
    17 => 'Aiki-4S',
    18 => 'Aiki-5',
  ),
  '@AK' => 
  array (
    0 => 'AK330',
    1 => 'AK330s',
    2 => 'AKAI NEO',
    3 => 'AKAI N8800',
    4 => 'AKAI PHA-4800',
    5 => 'AKAIPHA 5800',
    6 => 'AKAI MIDMA-7002S',
  ),
  '@AL' => 
  array (
    0 => 'alien jolla bionic',
    1 => 'alien ubuntu qt',
    2 => 'Alcatel A851L',
    3 => 'Alcatel one touch 908F',
    4 => 'Alcatel one touch 918',
    5 => 'Alcatel ONETOUCH 918',
    6 => 'ALCATEL OT 919',
    7 => 'Alcatel one touch 990',
    8 => 'Alcatel one touch 990S',
    9 => 'Alcatel OT M\'pop 5020D',
    10 => 'Alcatel 5098O',
    11 => 'Alcatel 7046T',
    12 => 'Alcatel 7049D',
    13 => 'Alcor Access Q913M',
    14 => 'Alcor Zest Q813I',
    15 => 'Alcor Zest Q813IS',
    16 => 'Alcor Zest Q813IX',
    17 => 'Alcor Zest Q933R',
    18 => 'ALLVIEW A4ALL',
    19 => 'ALLVIEW A6 Quad',
    20 => 'ALLVIEW P1',
    21 => 'Allview P2',
    22 => 'ALLVIEW P4',
    23 => 'ALLVIEW P4i',
    24 => 'Allview P5',
    25 => 'Allview P5-Mini',
    26 => 'ALLVIEW P5 Quad',
    27 => 'ALLVIEW P5 Qmax',
    28 => 'ALLVIEW P6',
    29 => 'ALLVIEW P6 Quad',
    30 => 'ALLVIEW TX1 Quasar',
    31 => 'ALLVIEW X2 Soul',
    32 => 'ALLVIEW-X3 Soul',
    33 => '(Allview|Alldro)!!',
    34 => 'Allwinner A10',
    35 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    36 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    37 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    38 => 'Alfa A93 POP',
    39 => 'Alfa L',
    40 => 'Ally',
    41 => 'Altus 18',
    42 => 'Altus 24',
  ),
  '@AM' => 
  array (
    0 => 'amd brazos',
    1 => 'amd persimmon',
    2 => 'Amlogic M1 reference board',
    3 => 'AML8726M',
    4 => 'AMLOGIC8726MX',
    5 => 'AM335XEVM',
    6 => 'AM335XEVM SK',
    7 => 'am3517evm',
    8 => 'AM437XEVM',
    9 => 'AM-H200',
    10 => 'Amazon Kindle Fire!',
    11 => 'Amazon Otter',
    12 => 'Amazon OtterX',
    13 => 'Amazon Otter2',
    14 => 'Amazon Tate',
    15 => 'Amazon Jem',
    16 => 'AMI-TT2',
    17 => 'AMI-TT2C',
    18 => 'AMI-TT2E',
    19 => 'AMI-TT4',
    20 => 'AMI-TTS',
    21 => 'AMI-TTLITE',
    22 => 'AMI-TTFORCE',
    23 => 'AML-MX REF',
    24 => '(AMO-)?AMOI!!',
    25 => 'AMD120',
    26 => '(Amaze|Hitech)!!',
    27 => 'Amaze 4G',
    28 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    29 => '(Amazing|Fantastic)!!',
  ),
  '@AN' => 
  array (
    0 => 'Android',
    1 => 'Android-for-Rockchip-2818',
    2 => 'AN7CG2',
    3 => 'AN7G2',
    4 => 'AN7G2DTE',
    5 => 'AN7G2I',
    6 => 'AN7G3',
    7 => 'AN7BG2',
    8 => 'AN7BG2DT',
    9 => 'AN7BG3',
    10 => 'AN7CG3',
    11 => 'AN7DG3',
    12 => 'AN7DG3B',
    13 => 'AN7DG3-CP',
    14 => 'AN7DG3ST-CP',
    15 => 'AN7FG3',
    16 => 'AN7HG3',
    17 => 'AN7IG3',
    18 => 'AN7SP',
    19 => 'AN8G2',
    20 => 'AN8G2I',
    21 => 'AN8G3',
    22 => 'AN8BG2',
    23 => 'AN8BG3',
    24 => 'AN8CG3',
    25 => 'AN9G2',
    26 => 'AN9G2I',
    27 => 'AN9G3',
    28 => 'AN10G2',
    29 => 'AN10G2-KN',
    30 => 'AN10G2-LN',
    31 => 'AN10G2I',
    32 => 'AN10BG2',
    33 => 'AN10BG2DT',
    34 => 'AN10BG2I',
    35 => 'AN10BG3',
    36 => 'AN10BG3DT',
    37 => 'AN10CG3',
    38 => 'AN10DG3',
    39 => 'AN13FP',
    40 => 'AN8BG3-LZ',
    41 => 'AN10G2-LZ',
    42 => 'AN10BG3-LZ',
    43 => 'AN7DG3C',
    44 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    45 => '(iBall )?Andi!!',
    46 => 'AND1',
    47 => 'AND1E',
    48 => 'AND1E TV',
    49 => 'Android edition by sfr STARADDICT',
    50 => 'Android Edition Starnaute',
    51 => 'Android Edition StarText',
    52 => 'Android Edition StarTrail',
    53 => '(Smartfren|Andromax)!!',
    54 => 'Androtab 7',
    55 => '(Starway )?Andromeda!!',
    56 => '(Andy|Yezz)!!',
  ),
  '@AO' => 
  array (
    0 => 'AOLE 828',
    1 => 'AOSON G18',
    2 => 'Aoson M11',
    3 => 'Aoson M19',
    4 => 'aoson m33',
    5 => 'aoson M33 3G',
    6 => 'AOSON M82T',
    7 => 'AOSON M97F',
    8 => 'AOSON M106NB',
    9 => 'AOSON M701TG-C',
    10 => 'AOSON M706T',
    11 => 'AOSON M721S',
    12 => 'aoson M787T',
    13 => 'AOSON M1016',
    14 => '(cm|aokp) tenderloin!',
    15 => 'AO5510',
  ),
  '@AP' => 
  array (
    0 => 'App Runtime for Chrome',
    1 => 'apollo',
    2 => 'Apanda.A60!',
    3 => 'apanda-A80S',
    4 => 'apanda A80S',
    5 => 'apanda-A80T',
    6 => 'apanda-A100',
    7 => 'apanda-A101',
    8 => 'Aprix D97+',
  ),
  '@AQ' => 
  array (
    0 => '(bq|Aquaris|Edison|Maxwell)!!',
    1 => 'Aquila 070-0508',
    2 => 'Aquila 070-0508 3G',
    3 => 'Aquila 080-0508',
    4 => 'AQUILA 080-1008',
    5 => 'Aquila 097-0508',
    6 => 'AQUILA 097-1006',
    7 => 'AQUILA 097-1016',
    8 => 'AQUILA 097-1016 BT + 3G',
    9 => 'AQUILA 097-1016 BT 3G',
    10 => 'Aquila 101',
    11 => 'Aquila 101-1008 3G',
    12 => 'Aquila LE 080-0508',
    13 => 'Aquila SE 090-0508',
    14 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    15 => '(Intex )?Aqua!!',
    16 => 'AQUOS!!',
    17 => 'aqua',
    18 => 'AQT80',
  ),
  '@AR' => 
  array (
    0 => 'ARCHM901',
    1 => 'Arnova 7G2',
    2 => 'ARNOVA8G2',
    3 => 'Arnova 10G2',
    4 => 'ARNOVA 90G3',
    5 => 'ARNOVA 90 G4',
    6 => 'ARNOVA 97G4',
    7 => 'ARNOVA 101 G4',
    8 => 'ARCHOS 50PL',
    9 => 'ARCHOS 79XE',
    10 => 'Archos5',
    11 => 'ARCHOS 70it2',
    12 => 'ARCHOS 70it2G8',
    13 => 'Archos 80 Internet Tablet',
    14 => 'Archos 101 Internet Tablet',
    15 => 'Archos!!',
    16 => 'Artes D708',
    17 => 'Artes D821',
    18 => 'Artes i701',
    19 => 'ARTES I709',
    20 => 'Artes Q812',
    21 => 'Arya A1+',
    22 => 'Arya Z2',
    23 => 'ARMM2V',
    24 => 'ARMM3V',
    25 => 'arrowsM03',
    26 => 'ARIES 101',
    27 => 'ARIES 785',
    28 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    29 => 'ARA YS608',
    30 => 'Arctic450',
    31 => 'Arc',
    32 => 'Arc S',
    33 => 'Armor 2',
    34 => 'Armor 3W',
    35 => 'Armor 3WT',
    36 => 'Armor 5S',
    37 => 'Armor 6E',
    38 => 'Armor 7',
    39 => 'Armor 7E',
    40 => 'Armor 8',
    41 => 'Armor 9',
    42 => 'Armor 9E',
    43 => 'Armor 10 5G',
    44 => 'Armor 11 5G',
    45 => 'Armor 11T 5G',
    46 => 'Armor X5',
    47 => 'Armor X7',
    48 => 'Armor X7 Pro',
    49 => 'Armor X8',
  ),
  '@AS' => 
  array (
    0 => 'ASUS Chromebook Flip!',
    1 => 'Astone A108',
    2 => 'aspire1000s',
    3 => 'Aspire!!',
    4 => 'ASK SP[0-9]{3,3}!!',
    5 => 'ASTRI',
    6 => 'asus laptop',
    7 => 'ASUS T20',
    8 => 'ASUS Pad ME370T',
    9 => 'Asus Fonepad ME371MG 8GB',
    10 => 'AST21',
    11 => 'asus google cube',
    12 => 'ASP-4300W',
    13 => 'ASP-4500Z',
    14 => 'ASP-5000H',
    15 => 'Asus A10',
    16 => '(Huawei|Ascend|HW-)!!',
    17 => 'AS740',
    18 => 'AS870 4G',
    19 => 'AS985',
    20 => 'AS650C',
    21 => 'Aster',
    22 => 'Aster T',
  ),
  '@AT' => 
  array (
    0 => 'AT390',
    1 => 'AT102HC',
    2 => 'AT104GS',
    3 => 'AT107F',
    4 => 'AT108F',
    5 => 'AT197F',
    6 => 'AT10H-A10WP',
    7 => 'AT101-014',
    8 => 'AT101T-114',
    9 => 'AT1A*',
    10 => 'AT1C',
    11 => 'AT1C*',
    12 => 'AT1D',
    13 => 'AT1G*',
    14 => 'AT7E',
    15 => 'ATP515CKIT',
    16 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    17 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    18 => 'aTab9.7 IPS',
    19 => 'AT735',
    20 => 'Atrix 2',
    21 => 'Atrix 2 WeifanZ',
    22 => 'Atrix 4G',
    23 => 'Atrix 4G ME860',
    24 => 'Atrix 4G MB860',
    25 => 'Atrix HD',
    26 => 'AtrixHD',
    27 => 'ATLANTIS',
    28 => 'ATP7526',
    29 => 'ATP7683',
    30 => 'AT1S0',
    31 => 'AT7-A',
    32 => 'AT7-B',
    33 => 'AT7-C',
    34 => 'AT10-A',
    35 => 'AT10LE-A',
    36 => 'AT10PE-A',
    37 => 'AT100',
    38 => 'AT200',
    39 => 'AT270',
    40 => 'AT300',
    41 => 'AT300SE',
    42 => 'AT330',
    43 => 'AT374',
    44 => 'AT400',
    45 => 'AT470',
    46 => 'AT500',
    47 => 'AT500a',
    48 => 'AT503',
    49 => 'AT570',
    50 => 'AT703',
    51 => 'AT830',
    52 => 'AT-A[QS][0-9]{2,2}!!',
    53 => 'ATLAS W',
  ),
  '@AU' => 
  array (
    0 => 'Aurora-II',
    1 => 'AUX!!',
    2 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    3 => '(Aura|iberry|AUXUS)!!',
    4 => 'AURUS III',
  ),
  '@AV' => 
  array (
    0 => 'Avea!!',
    1 => '(Ilium )?Avvio!!',
  ),
  '@AX' => 
  array (
    0 => 'AX3Party',
    1 => 'AX4Nano plus',
    2 => '(Axioo[\\- ])?PICO!!',
    3 => '(AXPAD|Axxion)!!',
    4 => '(Bmobile )?AX[0-9]{3,4}!!',
    5 => 'AX7OO',
    6 => 'AX600C',
  ),
  '@AZ' => 
  array (
    0 => 'Azumi!!',
    1 => '(AZ|BT)[0-9]{3,3}!!',
  ),
  '@B1' => 
  array (
    0 => 'B1-[A0-9][A0-9]{2,2}!!',
    1 => 'B15',
    2 => 'B15Q',
  ),
  '@B2' => 
  array (
    0 => 'B2021',
  ),
  '@B3' => 
  array (
    0 => 'B3-A10',
    1 => 'B3-A20',
    2 => 'B3-A20B',
    3 => 'B3-A30',
    4 => 'B3-A32',
    5 => 'B3-A40',
    6 => 'B3-A40FHD',
    7 => 'B3-A50FHD',
    8 => 'B3000',
  ),
  '@B5' => 
  array (
    0 => 'B5532',
  ),
  '@B9' => 
  array (
    0 => 'B916C',
  ),
  '@BA' => 
  array (
    0 => 'Bay Trail Generic Platform',
    1 => 'Bayley Bay',
    2 => 'baoxue',
    3 => 'Barnes & Noble Nook Tablet',
    4 => 'Barnes & Noble Nook HD',
    5 => 'Barnes & Noble Nook HD+',
    6 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    7 => 'BAH-(W09|AL00|L09)!',
    8 => 'BAH2-(AL10|L09|W09|W19)!',
    9 => 'BAH3-(W09)!',
    10 => '(HW-|HUAWEI )?BAC-(AL00|L03|L21|L22|TL00)!',
    11 => 'Backflip',
    12 => 'Backflip Me600',
    13 => 'Bambook S1',
    14 => 'BARRY',
    15 => 'BASE!!',
  ),
  '@BB' => 
  array (
    0 => 'BB EH7',
    1 => 'BB EH7LE',
    2 => '(BB )?Q5$!',
    3 => '(BB )?Q10$!',
    4 => '(BB )?Z10$!',
    5 => '(BB )?Z30$!',
    6 => 'BBA100-[0-9]!',
    7 => 'BBB100-[0-9]!',
    8 => 'BBC100-[0-9]!',
    9 => 'BBD100-[0-9]!',
    10 => 'BBE100-[0-9]!',
    11 => 'BBF100-[0-9]!',
    12 => '(BBG-|VIV-)?vivo!!',
    13 => 'Bbox Miami',
  ),
  '@BC' => 
  array (
    0 => 'bcm platform',
    1 => 'bcm7231',
    2 => 'bcm7425',
    3 => 'bcm7429',
    4 => 'bcm7435',
    5 => 'bcm7439',
    6 => 'bcm7445',
    7 => 'bcm7584',
    8 => 'BCM21654!',
    9 => 'BCM21664!',
    10 => 'BCM23550!',
    11 => 'BCM28145!',
    12 => 'BCM28155!',
    13 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
    14 => 'BC1003',
  ),
  '@BD' => 
  array (
    0 => '(BDS|BHX|BKO)\\-!!',
  ),
  '@BE' => 
  array (
    0 => 'BEAGLEBONE',
    1 => 'BeagleBone Black',
    2 => 'BEAGLEBONEBLACK',
    3 => 'BEAGLEBONEBLACK.A5C',
    4 => 'berlin',
    5 => 'berlin bg2!',
    6 => 'berlin generic!',
    7 => 'BenQ JD-130',
    8 => 'BenQ Android TV',
    9 => 'BenWee 5100',
    10 => 'BEE 9500',
    11 => 'BenQ!!',
    12 => 'BenWee!!',
    13 => 'BePhone UN030',
    14 => 'Best ?sonny!!',
    15 => 'BEAN 454',
    16 => 'BE202[69]!',
    17 => 'BE2013',
    18 => 'beetle',
    19 => 'Be Touch 2',
    20 => 'Beeline!!',
  ),
  '@BF' => 
  array (
    0 => 'BF[ \\-]!!',
    1 => 'BFB!!',
    2 => 'BF[0-9]{2,2}00!!',
  ),
  '@BG' => 
  array (
    0 => 'bg2 !',
    1 => 'bg2ct !',
    2 => 'bg2q4k !',
    3 => 'bg2qa0v4 !',
    4 => 'bg2qv4 !',
    5 => 'BGO-(DL09|L03)!',
    6 => 'BG2-(U01|W09)!',
  ),
  '@BH' => 
  array (
    0 => '(BDS|BHX|BKO)\\-!!',
  ),
  '@BI' => 
  array (
    0 => 'bird75v2',
    1 => 'BioniQ!!',
    2 => 'Bird!!',
    3 => 'BizSmartPhone',
    4 => 'BIP-6000',
    5 => 'BISON',
    6 => 'BISON Pro',
    7 => 'BISON GT',
    8 => 'BIRDY',
  ),
  '@BK' => 
  array (
    0 => '(BDS|BHX|BKO)\\-!!',
    1 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@BL' => 
  array (
    0 => 'Blaze',
    1 => 'Blaze Tablet',
    2 => 'BlueStacks!',
    3 => 'BlackBerry Runtime for Android Apps',
    4 => 'BLACKBERRY P\'9982',
    5 => 'BL6000Pro',
    6 => '(BLU|DASH|LIFE|NEO|STUDIO|VIVO)!!',
    7 => 'Bluboo S1',
    8 => 'Blaze S180',
    9 => 'BLADE LE 70',
    10 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    11 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    12 => '(HW-|HUAWEI )?(BLL|CHC|TAG|KII)!!',
    13 => '(BLF-)?lephone!!',
    14 => 'Blackphone 2',
    15 => 'BLOOM',
    16 => 'BLACK',
    17 => '(ZTE )?Blade!!',
  ),
  '@BM' => 
  array (
    0 => 'BMOBILE DASH 3.5',
    1 => '(Bmobile )?AX[0-9]{3,4}!!',
    2 => 'Bmobile T35AC',
    3 => 'BM999',
  ),
  '@BN' => 
  array (
    0 => '(NOOK )?BNRV(200|300)!',
    1 => '(NOOK )?BNTV250!',
    2 => '(NOOK )?BNRV350!',
    3 => '(NOOK )?BNTV(400)!',
    4 => '(NOOK )?BNTV(450)!',
    5 => '(NOOK )?BNTV(600)!',
    6 => '(NOOK )?BNTV(800)!',
    7 => 'BN Nook HD',
    8 => 'BN NookHD+',
    9 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@BO' => 
  array (
    0 => 'Boost',
    1 => 'BO-FRSP4',
    2 => 'BO-LFSPBS5',
    3 => 'BOVO S-F16',
    4 => 'BOWAY!!',
    5 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    6 => 'Bouygues Telecom Bs 351',
    7 => 'Bouygues Telecom Bs 402',
    8 => 'Bouygues Telecom Bs 403',
    9 => 'Bouygues Telecom Bs 451',
    10 => 'Boston 4G',
    11 => 'Boston',
  ),
  '@BP' => 
  array (
    0 => 'BP710A',
  ),
  '@BQ' => 
  array (
    0 => '(bq|Aquaris|Edison|Maxwell)!!',
    1 => 'BQS-3510',
    2 => 'BQS-3552',
    3 => 'BQS-4001',
    4 => 'BQS-4004',
    5 => 'BQS-4005',
    6 => 'BQS-4007',
    7 => 'BQS-4008',
    8 => 'BQS-4009',
    9 => 'BQS-4010',
    10 => 'BQS-4501 Bristol',
    11 => 'BQS-4502',
    12 => 'BQS-4503',
    13 => 'BQS-4510',
    14 => 'BQS-4515',
    15 => 'BQS-4516',
    16 => 'BQS-4525',
    17 => 'BQS-4552',
    18 => 'BQS-4555',
    19 => 'BQS-4560',
    20 => 'BQS-4700',
    21 => 'BQS-4701',
    22 => 'BQS-4702',
    23 => 'BQS-4707',
    24 => 'BQS-4800',
    25 => 'BQS-5000 Tokyo',
    26 => 'BQS-5003',
    27 => 'BQS-5004',
    28 => 'BQS-5005',
    29 => 'BQS-5009',
    30 => 'BQS-5001',
    31 => 'BQS 5007',
    32 => 'BQS-5010',
    33 => 'BQS-5011',
    34 => 'BQS-5020',
    35 => 'BQS-5025',
    36 => 'BQS-5040',
    37 => 'BQS-5045',
    38 => 'BQS-5050',
    39 => 'BQS-5065',
    40 => 'BQS-5070',
    41 => 'BQS-5200',
    42 => 'BQS-5500',
    43 => 'BQS-5502',
    44 => 'BQS-5505',
    45 => 'BQS 5505',
  ),
  '@BR' => 
  array (
    0 => 'Bravo',
    1 => 'BRAVIA 2015',
    2 => 'BRAVIA 4K 2015',
    3 => 'BRAVIA 4K GB',
    4 => 'BroadSign Xpress!',
    5 => 'Broncho M7',
    6 => '(BRS-)?BROR!!',
    7 => 'BRAVIA',
  ),
  '@BS' => 
  array (
    0 => 'Bs 451',
    1 => 'Bs 501',
    2 => 'Bs541',
    3 => 'BSTB-200C',
  ),
  '@BT' => 
  array (
    0 => 'BTV-(DL09|W09)!',
    1 => '(AZ|BT)[0-9]{3,3}!!',
  ),
  '@BU' => 
  array (
    0 => 'BUSH!!',
    1 => 'Burst S280',
    2 => 'Butterfly S',
    3 => 'Bucare Y330-U05',
  ),
  '@BV' => 
  array (
    0 => 'BV4900Pro',
    1 => 'BV5500Plus',
    2 => 'BV5900',
    3 => 'BV6300Pro',
    4 => 'BV6600',
    5 => 'BV6900',
    6 => 'BV8000Pro',
    7 => 'BV9100',
    8 => 'BV9500',
    9 => 'BV9500Plus',
    10 => 'BV9600E',
    11 => 'BV9600Pro',
    12 => 'BV9800Pro',
    13 => 'BV9900E',
    14 => 'BV9900Pro',
  ),
  '@C1' => 
  array (
    0 => 'C15 Pro',
  ),
  '@C2' => 
  array (
    0 => 'C21 Pro',
    1 => 'C2',
  ),
  '@C5' => 
  array (
    0 => '(GIO-)?(GiONEE[- ])?C500!',
    1 => 'C525c',
  ),
  '@C6' => 
  array (
    0 => 'C6 Duo',
    1 => 'C6Quad 4G',
    2 => '(GIO-)?(GiONEE[- ])?C600!',
    3 => '(GIO-)?(GiONEE[- ])?C610!',
    4 => '(GIO-)?(GiONEE[- ])?C620!',
  ),
  '@C7' => 
  array (
    0 => 'C771',
    1 => '(GIO-)?(GiONEE[- ])?C700!',
  ),
  '@C8' => 
  array (
    0 => 'C811 4G',
    1 => 'C8297W',
    2 => 'C8660',
    3 => '(GIO-)?(GiONEE[- ])?C800!',
    4 => 'C8817D',
    5 => 'C8817E',
    6 => 'C8[0-9]{3,3}!!',
    7 => 'C868',
  ),
  '@C9' => 
  array (
    0 => 'C908',
  ),
  '@CA' => 
  array (
    0 => 'Cardhu',
    1 => 'CA907AAC0G',
    2 => 'Carbon 1 Mark II',
    3 => 'Camangi-Mangrove7',
    4 => 'CAPTIVA!!',
    5 => 'Carpad T5',
    6 => 'Carpad T61 2g',
    7 => 'CAL21',
    8 => 'CA201SP',
    9 => 'CA-201L',
    10 => 'Casper VIA!!',
    11 => 'CAT!!',
    12 => 'Candy TV',
    13 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    14 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    15 => '(HW-|HUAWEI )?CAZ-(AL10|TL10|TL20)!',
    16 => '(HW-|HUAWEI )?CAN-(L01|L11|L12)!',
    17 => 'calgary',
    18 => 'Captivate-I897',
    19 => 'capricorn',
  ),
  '@CC' => 
  array (
    0 => 'CCE SK352',
  ),
  '@CD' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
  ),
  '@CE' => 
  array (
    0 => 'cedartrail',
    1 => 'CENTURION',
    2 => 'CENTURION 3',
    3 => 'Celkon!!',
  ),
  '@CH' => 
  array (
    0 => 'Chacer',
    1 => 'ChangHong!!',
    2 => 'CHER!!',
    3 => 'Cherry Mobile Burst',
    4 => 'Cherry Life',
    5 => 'Cherry Razor',
    6 => 'Cherry Mobile Razor',
    7 => 'CHERRY SNAP',
    8 => 'Cherry Sonic',
    9 => 'Cherry thunder2.0',
    10 => 'Cherry Mobile Amber W380',
    11 => 'Cherry w500',
    12 => 'CHUWI!!',
    13 => 'CHONG!!',
    14 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    15 => '(HW-|HUAWEI )?(BLL|CHC|TAG|KII)!!',
    16 => 'chagall',
    17 => 'chiron',
  ),
  '@CI' => 
  array (
    0 => 'cius',
    1 => 'CIUS-7!',
    2 => 'Citycall!!',
    3 => 'CINK',
    4 => 'CINK+',
    5 => 'CINK FIVE',
    6 => 'CINK KING',
    7 => 'CINK PEAX',
    8 => 'CINK PEAX+',
    9 => 'CINK PEAX 2',
    10 => 'CINK SLIM',
    11 => 'Cink Slim A',
    12 => 'Cink Slim B',
    13 => 'CINK SLIM 2',
  ),
  '@CJ' => 
  array (
    0 => '(CJ-)?ThL!!',
  ),
  '@CL' => 
  array (
    0 => 'Clarion Mirage 2',
    1 => '(Cloudfone|CloudPad|Excite|Thrill)!!',
    2 => 'Clanga 079-1016',
    3 => 'Clanga 097-2016',
    4 => 'Clanga SE 097-1008',
    5 => 'CLP281X',
    6 => 'Click',
    7 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    8 => '(Intex )?Cloud!!',
    9 => 'CLIQ',
    10 => 'CLIQ XT',
    11 => 'CLIQ2',
  ),
  '@CM' => 
  array (
    0 => 'CMP!!',
    1 => '(cm|aokp) tenderloin!',
    2 => 'CMR-(AL09|AL19|W09|W19)!',
    3 => 'CM980',
    4 => 'CM990',
  ),
  '@CN' => 
  array (
    0 => 'CnM!!',
    1 => 'CN51 [NQ]!',
  ),
  '@CO' => 
  array (
    0 => 'Colibri-T20',
    1 => 'Commodore!!',
    2 => 'Commtiva-HD710',
    3 => 'Commtiva-N700',
    4 => 'Connspeed IP71',
    5 => '(Colorfly|CT[0-9]{3,3})!!',
    6 => 'Colors!!',
    7 => 'ConCorde!!',
    8 => 'Coolgen!!',
    9 => 'Coolpad!!',
    10 => 'COSHIP F2',
    11 => 'COWON!!',
    12 => 'COZCO C1 Plus',
    13 => 'Core-M5',
    14 => 'Collo DG100',
    15 => 'Collo 2 DG120',
    16 => 'Connect-2G-2.0',
    17 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    18 => 'Coquettish Red',
    19 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    20 => 'Comet',
    21 => 'Corvair',
    22 => 'COOLPIX S800c',
    23 => 'COOLPIX S810c',
    24 => 'Constellation V',
  ),
  '@CP' => 
  array (
    0 => 'CP-DX!!',
    1 => 'CP3700A',
    2 => 'CP8676!',
    3 => 'CP8298!',
    4 => 'CPN-(W09|AL00|L09)!',
    5 => 'CPH1803',
    6 => 'CPH1809',
    7 => 'CPH1901',
    8 => 'CPH1937',
    9 => 'CPH1941',
    10 => 'CPH1605',
    11 => 'CPH1701',
    12 => 'CPH1717',
    13 => 'CPH1801',
    14 => 'CPH1729',
    15 => 'CPH1827',
    16 => 'CPH1609',
    17 => 'CPH1613',
    18 => 'CPH1723',
    19 => 'CPH1727',
    20 => 'CPH1819',
    21 => 'CPH1821',
    22 => 'CPH1859',
    23 => 'CPH1881',
    24 => 'CPH1823',
    25 => 'CPH1831',
    26 => 'CPH1837',
    27 => 'CPH1871',
    28 => 'CPH1875',
    29 => 'CPH1877',
    30 => 'CPH1893',
    31 => 'CPH1903',
    32 => 'CPH1907',
    33 => 'CPH1909',
    34 => 'CPH1911',
    35 => 'CPH1917',
    36 => 'CPH1919',
    37 => 'CPH1920',
    38 => 'CPH1923',
    39 => 'CPH1931',
    40 => 'CPH1933',
    41 => 'CPH1951',
    42 => 'CPH1969',
    43 => 'CPH1989',
    44 => 'CPH2005',
    45 => 'CPH2009',
    46 => 'CPH2021',
    47 => 'CPH2023',
    48 => 'CPH2025',
    49 => 'CPH2043',
    50 => 'CPH2065',
    51 => 'CPH2067',
    52 => 'CPH2069',
    53 => 'CPH2083',
    54 => 'CPH2089',
    55 => 'CPH2091',
    56 => 'CPH2121',
    57 => 'CPH2125',
    58 => 'CPH2127',
    59 => 'CPH2135',
    60 => 'CPH2145',
    61 => 'CPH2161',
    62 => 'CPH2173',
    63 => 'CPH2185',
    64 => 'CPH2195',
    65 => 'CPH2197',
    66 => 'CPH2205',
    67 => 'CPH2207',
    68 => 'CPH2211',
    69 => 'CPH2219',
    70 => 'CPH2247',
    71 => 'CPH2251',
    72 => 'CPH2269',
    73 => 'CPH2271',
    74 => 'CPH2273',
    75 => 'CPH2305',
    76 => 'CPH2307',
    77 => 'CPH2371',
    78 => 'CPH1721',
    79 => 'CPH1611',
    80 => 'CPH1879',
    81 => 'CPH1861',
    82 => 'CPH1979',
  ),
  '@CR' => 
  array (
    0 => 'Crespo!',
    1 => 'CRESTA.CTP888',
    2 => 'Cross!!',
    3 => 'Crosscall!!',
    4 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    5 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    6 => 'CRONO 22',
    7 => 'Crescent',
  ),
  '@CS' => 
  array (
    0 => 'CS45XA',
    1 => 'CS-1A13',
    2 => '(CSL[- ])?(Spice[- ]?)?Mi(-| )?[0-9]{3,3}!!',
  ),
  '@CT' => 
  array (
    0 => 'CT[0-9]{3,4}!!',
    1 => 'CT-1',
    2 => 'CT910',
    3 => '(Colorfly|CT[0-9]{3,3})!!',
    4 => 'CTP828BT',
    5 => '(GIO-)?(GiONEE[- ])?Ctrl V1!',
    6 => '(GIO-)?(GiONEE[- ])?Ctrl V2!',
    7 => '(GIO-)?(GiONEE[- ])?Ctrl V3!',
    8 => '(GIO-)?(GiONEE[- ])?Ctrl V4!',
    9 => '(GIO-)?(GiONEE[- ])?Ctrl V5!',
    10 => 'CT50',
    11 => 'CT720G',
    12 => 'CT720HD',
    13 => 'CT920',
    14 => 'CT1006',
    15 => 'CT9973W43M',
  ),
  '@CU' => 
  array (
    0 => 'CUBOT!!',
    1 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
    2 => 'CUBE!!',
    3 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
  ),
  '@CW' => 
  array (
    0 => 'CW[- ]!!',
  ),
  '@CX' => 
  array (
    0 => 'CX-919',
    1 => 'CX-921',
    2 => 'CX-921B',
    3 => 'CX-929',
    4 => 'CX-950',
    5 => 'CX-958',
  ),
  '@CY' => 
  array (
    0 => 'Cydle M7!',
    1 => 'Cynus!!',
    2 => 'Cyclone!!',
  ),
  '@D-' => 
  array (
    0 => 'd-01G',
    1 => 'd-01H',
    2 => 'd-01J',
    3 => 'd-02H',
  ),
  '@D0' => 
  array (
    0 => 'D000-0000(13|18|19)-[0-9]{3,3}!',
    1 => 'D000-0000(01|07)-[A-Z][0-9]{2,2}!',
    2 => 'D000-000002-[W0][0-9]{2,2}!',
    3 => 'D000-0000(10|11)-N!',
    4 => 'D000 000043',
  ),
  '@D2' => 
  array (
    0 => 'D2-[0-9]{3,3}!!',
    1 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
  ),
  '@D5' => 
  array (
    0 => 'D530',
    1 => 'D539',
  ),
  '@D7' => 
  array (
    0 => 'D709',
    1 => 'D7800AD',
    2 => 'D75E',
    3 => 'D70W',
  ),
  '@D8' => 
  array (
    0 => 'D820X',
    1 => 'D816(d|t|v|w|x)!',
    2 => 'D816G',
    3 => 'D820mt',
    4 => 'D80',
  ),
  '@D9' => 
  array (
    0 => 'D9702',
    1 => 'D91',
  ),
  '@DA' => 
  array (
    0 => 'DA2[0-9]{2,2}[A-Z]+!!',
    1 => '(BLU|DASH|LIFE|NEO|STUDIO|VIVO)!!',
    2 => 'Dakele A380',
    3 => 'DAKELE MC001',
    4 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    5 => 'DATAM803HC',
    6 => 'DATAM805HC',
    7 => 'DATAM819HD!',
    8 => 'DARKFULL',
    9 => 'DARKMOON',
    10 => 'DARKNIGHT',
    11 => 'DARKSIDE',
  ),
  '@DE' => 
  array (
    0 => 'Dell!!',
    1 => 'Dell XCD35',
    2 => 'DEM752HCF',
    3 => '(DENVER-)?TA[CD]-[0-9]{4,5}!!',
    4 => 'deovo V5',
    5 => 'DESAY!!',
    6 => 'DEXP Ixion ML 5',
    7 => 'DEXP Ixion XL 5',
    8 => 'desire200',
    9 => 'Desire 310 dual sim',
    10 => 'Desire 500',
    11 => 'Desire 500 Plus',
    12 => 'Desire 510',
    13 => 'Desire 610',
    14 => 'Desire 619d',
    15 => 'Desire 816',
    16 => 'Desire 816 dual sim',
    17 => 'Desire ?HD!',
    18 => 'desirec',
    19 => 'Desire L by HTC',
    20 => 'Desire ?S!',
    21 => 'Desire X',
    22 => 'Desire Z',
    23 => 'Desire!',
    24 => 'DEOX',
    25 => 'DEFY',
    26 => 'Defy(\\+| Plus)!',
    27 => 'DEDY+',
    28 => 'Dext',
    29 => 'Devour',
    30 => 'DEM752NC',
  ),
  '@DG' => 
  array (
    0 => 'DG310',
    1 => 'DG750 Iron Bone',
  ),
  '@DI' => 
  array (
    0 => 'Dialog!!',
    1 => 'DIT[0-9]{4,6}!!',
    2 => 'DIGICELDL1plus',
    3 => 'DIGICEL DL800',
    4 => 'DIGICEL DL1000',
    5 => '(Digma )?iD[jmnsx][DQ]?[0-9]*!!',
    6 => 'DISTAB9000R',
    7 => 'DishTV Smartvu A2020',
    8 => 'Discovery DG500',
    9 => 'Discovery2-DG500C',
    10 => '(HW-|HUAWEI )?DIG-(AL00|L01|L03|L21|TL10)!',
    11 => 'DIAMOND D1',
    12 => 'DIAMOND S3',
    13 => 'Discovery',
    14 => 'Discovery Elite',
    15 => 'Discovery II Mini',
  ),
  '@DJ' => 
  array (
    0 => 'DJC Touchtab3',
    1 => 'DJC Touchtab4',
  ),
  '@DK' => 
  array (
    0 => 'DKL01',
    1 => 'Dk1031',
  ),
  '@DL' => 
  array (
    0 => 'DL750',
    1 => 'DL8006',
    2 => 'DLX',
    3 => 'DLXU',
    4 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    5 => 'dL1',
    6 => 'DLT-H0',
  ),
  '@DM' => 
  array (
    0 => 'DM0[0-9]{2,2}(K|SH)!!',
    1 => 'DM001c Frozen',
    2 => 'DM001c Mickey',
    3 => 'DM001c Princess',
    4 => 'DM-01G',
    5 => 'DM-01K',
    6 => 'DM-02H',
    7 => 'DMC-CM1',
    8 => 'DM-01H',
    9 => 'DM-01J',
  ),
  '@DN' => 
  array (
    0 => 'DNS Airbook TYT701',
    1 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
    2 => 'DNS S!!',
    3 => 'DNSS4003',
    4 => 'DNS4502M',
    5 => 'DN2101',
    6 => 'DN2103',
  ),
  '@DO' => 
  array (
    0 => 'Dooderbutt!',
    1 => 'domod G20',
    2 => 'Doogee X5 Max Pro',
    3 => 'DOOGEE-TITANS-DG150',
    4 => 'DOOV!!',
    5 => 'Doro Liberto!!',
    6 => 'Doro 8030',
    7 => 'Doro 8040',
    8 => 'Doro 824',
    9 => 'Doro PhoneEasy 745',
    10 => 'Dolphin 70e Black',
    11 => 'Docomo HT-03A',
  ),
  '@DP' => 
  array (
    0 => 'DPS Dream 7',
    1 => 'DPS Dream 9',
    2 => 'DP-X1',
    3 => 'DPH-D710',
  ),
  '@DR' => 
  array (
    0 => 'Dream',
    1 => '(GIO-)?(GiONEE[- ])?Dream D1!',
    2 => 'Droid Incredible',
    3 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    4 => 'DroniX-0.5',
    5 => 'Droid',
    6 => 'DROID',
    7 => 'DROID ?2!',
    8 => 'DROID ?3!',
    9 => 'DROID ?4!',
    10 => 'DroidPro',
    11 => 'DROID Pro',
    12 => 'DROID HD',
    13 => 'DROID BIONIC!',
    14 => 'DROID RAZR HD!',
    15 => 'DROID ?RAZR!',
    16 => 'DROID SPYDER',
    17 => 'DROID ?X2!',
    18 => 'DROID ?X!',
    19 => 'DROIDZ Duo',
    20 => 'DROIDZ Active 3G',
    21 => 'DROIDZ ATOM S',
    22 => 'DROIDZ Beat TV',
    23 => 'DROIDZ Drive+',
    24 => 'DROIDZ Excel',
    25 => 'DROIDZ Excite',
    26 => 'DROIDZ Force',
    27 => 'DROIDZ Match TV',
    28 => 'DROIDZ Mini Q',
    29 => 'DROIDZ Portal X',
    30 => 'DROIDZ Quad',
    31 => 'DROIDZ Race',
    32 => 'DROIDZ Race X',
    33 => 'DROIDZ Sport',
    34 => 'DROIDZ Ultimate',
  ),
  '@DS' => 
  array (
    0 => 'Dslide!!',
    1 => 'DSB-0220',
    2 => 'DSB-0230',
  ),
  '@DT' => 
  array (
    0 => 'Dtac phone Joey Jet 2',
    1 => 'dtab01',
  ),
  '@DU' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    1 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
  ),
  '@DV' => 
  array (
    0 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
  ),
  '@DW' => 
  array (
    0 => 'DW-PS3G5',
    1 => 'DW-UBT7W',
    2 => 'DW-UBT7SC*',
    3 => 'DW-UBT27Cz',
    4 => 'DW-UBT23G7',
  ),
  '@DY' => 
  array (
    0 => 'dyno 7.80',
    1 => 'dyno 7.85',
    2 => 'Dynamic Fun',
    3 => 'Dynamic Jump',
    4 => 'Dynamic Maxi',
    5 => 'Dynamic Milo',
    6 => 'Dynamic Racing 2',
    7 => 'Dynamic Raging Go',
    8 => 'Dynamic Shake',
    9 => 'Dynamic Wide',
    10 => 'Dynamic Wing',
    11 => 'Dynamic Racing 3',
  ),
  '@E' => 
  array (
    0 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
  ),
  '@E-' => 
  array (
    0 => '(E-Boda|Eruption|Essential|Supreme|Storm|Revo)!!',
  ),
  '@E1' => 
  array (
    0 => 'E1 v360',
    1 => 'E170BS',
    2 => 'E1031',
    3 => 'E1050X',
    4 => 'E1051X',
    5 => 'E1060X',
    6 => 'e1808 v75!',
    7 => 'e1109 v73!',
  ),
  '@E2' => 
  array (
    0 => 'E2 Jump',
    1 => 'E239',
    2 => 'E270BSA',
    3 => 'E2281',
    4 => '(Moto)?E2$!',
    5 => '(Moto)?E2\\(4G-LTE\\)$!',
  ),
  '@E3' => 
  array (
    0 => 'E3 Living',
    1 => 'E3 Sign',
    2 => '(GIO-)?(GiONEE[- ])?E3T!',
    3 => '(GIO-)?(GiONEE[- ])?E3$!',
  ),
  '@E4' => 
  array (
    0 => 'E4',
    1 => 'E4 Lite',
  ),
  '@E5' => 
  array (
    0 => '(GIO-)?(GiONEE[- ])?E5$!',
  ),
  '@E6' => 
  array (
    0 => '(GIO-)?(GiONEE[- ])?E6mini!',
    1 => '(GIO-)?(GiONEE[- ])?E6$!',
    2 => '(GIO-)?(GiONEE[- ])?E6T$!',
    3 => 'E6782',
    4 => 'E650',
    5 => 'E60w',
  ),
  '@E7' => 
  array (
    0 => 'E70[0-9]{2,2}H[DG]!!',
    1 => '(GIO-)?(GiONEE[- ])?E7$!',
    2 => 'E780',
    3 => 'E731',
  ),
  '@E8' => 
  array (
    0 => 'E80[0-9]{2,2}H[DG]!!',
    1 => 'E860',
    2 => 'E800',
    3 => 'E815',
    4 => 'E812',
    5 => 'E810',
  ),
  '@E9' => 
  array (
    0 => 'E940-2795-00',
    1 => 'E940-2796-00',
    2 => 'E900',
    3 => 'E912',
  ),
  '@EA' => 
  array (
    0 => 'eagle75v1 2',
    1 => 'EasyPhone EP5',
    2 => 'EasyPad 971',
  ),
  '@EB' => 
  array (
    0 => 'EBEN!!',
    1 => 'EBEST!!',
    2 => 'EB2101',
    3 => 'EB2103',
    4 => 'EB-4063-X',
    5 => 'EB-A71GJ',
    6 => 'EB-W51GJ',
    7 => 'EB-WX1GJ',
    8 => 'EB-L76G-B',
  ),
  '@EC' => 
  array (
    0 => '(Hisense )?(LED[0-9]{2,2}(G|K|L|EC|XT)[0-9]{2,3})!',
    1 => 'Eco9 v1',
    2 => 'Eco9 v2',
    3 => 'Eco11 v2',
  ),
  '@ED' => 
  array (
    0 => '(bq|Aquaris|Edison|Maxwell)!!',
    1 => 'edgejr',
    2 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    3 => 'edenTAB ET-701',
  ),
  '@EE' => 
  array (
    0 => 'eeepc',
    1 => 'eee 701',
    2 => '(Eee Pad )?Transformer Prime TF201!',
  ),
  '@EF' => 
  array (
    0 => 'EFM710A',
  ),
  '@EG' => 
  array (
    0 => 'EG98',
    1 => 'EG680',
    2 => 'EG968B',
  ),
  '@EI' => 
  array (
    0 => 'EIS01PT',
  ),
  '@EK' => 
  array (
    0 => 'EK-[GK][CN][0-9]{3,3}!!',
  ),
  '@EL' => 
  array (
    0 => 'EL72B',
    1 => 'ELF-II',
    2 => 'Elephone!!',
    3 => 'ELIYA S1',
    4 => 'ELIFE-E3',
    5 => 'Elife E5',
    6 => 'ElifeE6',
    7 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    8 => 'Elektra L',
    9 => 'Elektra XL',
    10 => 'Electrify',
    11 => 'Eluga A2',
    12 => 'ELUGA A3',
    13 => 'ELUGA A3 Pro',
    14 => 'ELUGA I2',
    15 => 'ELUGA I2 Activ',
    16 => 'ELUGA I4',
    17 => 'Eluga Arc 2',
    18 => 'Eluga Mark 2',
    19 => 'ELUGA Note',
    20 => 'ELUGA Prim',
    21 => 'ELUGA Pulse X',
    22 => 'ELUGA Ray',
    23 => 'ELUGA Ray Max',
    24 => 'ELUGA Ray X',
    25 => 'Eluga Ray 700',
    26 => 'ELUGA Turbo',
    27 => 'Elite',
    28 => 'Elite 11',
    29 => '(SENCOR )?ELEMENT!!',
    30 => 'Elite 5.5L+',
    31 => 'Elite 6.0L',
  ),
  '@EM' => 
  array (
    0 => 'EMBT3C',
    1 => 'EM63',
    2 => 'EM01F',
    3 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    4 => 'eMAX mini',
    5 => 'EMR1879',
  ),
  '@EN' => 
  array (
    0 => 'Endeavour!!',
    1 => 'ENR U',
    2 => 'EndeavorU',
    3 => 'Enjoy 71',
    4 => 'Enjoy 7 Plus',
  ),
  '@EP' => 
  array (
    0 => 'EPAD',
    1 => 'epade!!',
  ),
  '@EQ' => 
  array (
    0 => 'equiso.smart.tv.pro',
  ),
  '@ER' => 
  array (
    0 => '(E-Boda|Eruption|Essential|Supreme|Storm|Revo)!!',
    1 => 'Eris',
    2 => 'ERIS ADR6200',
    3 => 'era 2',
    4 => 'era X',
    5 => 'era1X',
    6 => 'Era 2X',
    7 => 'era HD',
    8 => 'era 4G',
    9 => 'era 4K',
  ),
  '@ES' => 
  array (
    0 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
    1 => '(E-Boda|Eruption|Essential|Supreme|Storm|Revo)!!',
    2 => 'eSTAR!!',
  ),
  '@ET' => 
  array (
    0 => 'ETBW11AA',
    1 => 'Etel!!',
    2 => 'ETOOSPAD5',
    3 => 'ETOOSPAD6',
    4 => 'ET1',
    5 => 'Etisalat Smartphone',
    6 => 'etab5',
  ),
  '@EV' => 
  array (
    0 => 'EVOKE HDI Box',
    1 => 'EVDO1000',
    2 => 'EvoPAD!!',
    3 => '(EVAS )?EVERCOSS[- ]A!!',
    4 => 'EVERPAD SC-710',
    5 => 'Ever!!',
    6 => 'EVOLVEO StrongPhone G7',
    7 => 'EVOLVEO StrongPhone G9',
    8 => 'EVO',
    9 => 'Evo HD2',
    10 => 'EVO ?3D!',
    11 => 'EVO 4G',
    12 => 'Evo V 4G',
    13 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    14 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    15 => '(EV|KM)-[ES][0-9]{3,3}!!',
    16 => 'Event',
    17 => 'EVERY35',
  ),
  '@EX' => 
  array (
    0 => 'Exynos5410',
    1 => '(Cloudfone|CloudPad|Excite|Thrill)!!',
    2 => '(Explay|X-tremer|ActiveD|Informer|Surfer)!!',
    3 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    4 => 'Explorer A310e',
  ),
  '@EZ' => 
  array (
    0 => 'Ezcast',
    1 => 'eZee!!',
  ),
  '@F&' => 
  array (
    0 => 'F&U ETB!!',
  ),
  '@F-' => 
  array (
    0 => 'F-[0-9]{2,2}[A-Z]!!',
    1 => 'F-074',
  ),
  '@F1' => 
  array (
    0 => 'f12[a-z]{3,3}!!',
    1 => '(GIO-)?(GiONEE[- ])?F103!',
    2 => 'F100',
    3 => 'F1f',
    4 => 'F1w',
    5 => 'F13',
    6 => 'F15',
  ),
  '@F3' => 
  array (
    0 => '(GIO-)?(GiONEE[- ])?F301!',
  ),
  '@F5' => 
  array (
    0 => 'F50Q',
  ),
  '@F6' => 
  array (
    0 => 'F600',
  ),
  '@FA' => 
  array (
    0 => 'FAR7',
    1 => 'FAR70B',
    2 => 'FARTM933KZ',
    3 => 'Fablet F3',
    4 => '(Amazing|Fantastic)!!',
  ),
  '@FC' => 
  array (
    0 => 'FC6100',
  ),
  '@FD' => 
  array (
    0 => 'FDT!!',
    1 => 'FDR-A01w',
  ),
  '@FE' => 
  array (
    0 => 'FEVER',
    1 => 'ferrari',
  ),
  '@FH' => 
  array (
    0 => 'FHMD001',
  ),
  '@FI' => 
  array (
    0 => 'FIH-FB0',
    1 => 'FIH-!!',
    2 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    3 => 'Find 5',
    4 => 'FIND7',
    5 => 'FINNEY U1',
  ),
  '@FJ' => 
  array (
    0 => 'FJ[LT][0-9]{2,2}!!',
    1 => 'FJJB091',
  ),
  '@FL' => 
  array (
    0 => 'Flo',
    1 => 'Flounder',
    2 => 'Flame2',
    3 => 'Flare S100',
    4 => 'Flare 2.0',
    5 => 'Flare 2.1',
    6 => 'Flare2X',
    7 => 'Flare 5',
    8 => 'FlareA1',
    9 => 'Flare J1',
    10 => 'FLARE J1 PLUS',
    11 => 'FLARE J2',
    12 => 'Flare J2 DTV',
    13 => 'Flare J3 Plus',
    14 => 'Flare P1 Mini',
    15 => 'Flare S3 Power',
    16 => 'Flare S4',
    17 => 'Flare S4 Max',
    18 => 'Flare S4 Mini',
    19 => 'FLARE S5 MAX',
    20 => 'Flare S6 Max',
    21 => 'Fly F[0-9]{2,3}!!',
    22 => 'Fly Phoenix 2',
    23 => 'Fly Ego Art 2',
    24 => '(Fly )?IQ ?[0-9]{3,4}!!',
    25 => 'Flylife!!',
    26 => 'Fly Flylife Web 7.85 Slim',
    27 => 'Flyer',
    28 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    29 => 'Flair E1',
    30 => 'Flair E2',
    31 => 'FlairE3',
    32 => 'Flair P1',
    33 => 'Flair P1i',
    34 => 'Flair P8',
    35 => 'Flair S1',
    36 => 'Flair Z1',
  ),
  '@FN' => 
  array (
    0 => '(tablet )?fnac!!',
  ),
  '@FO' => 
  array (
    0 => 'Forward Active',
    1 => 'Forward Art',
    2 => 'Forward Escape',
    3 => 'Forward Endurance',
    4 => 'ForwardEndurance',
    5 => 'FORWARD EVOLVE',
    6 => 'Forward Infinity',
    7 => 'Forward Next',
    8 => 'Forward Prime',
    9 => 'Forward Racing 3',
    10 => 'Forward Ruby',
    11 => 'ForwardRuby',
    12 => 'Forward Shake',
    13 => 'ForwardXtreme',
    14 => 'Forward Young',
    15 => 'ForwardZero',
    16 => 'Forward 5 5',
    17 => 'Forward 5.5',
    18 => 'Folio 100',
    19 => 'folio100',
  ),
  '@FP' => 
  array (
    0 => 'FP1',
    1 => 'FP1U',
    2 => 'FP2',
    3 => 'FP3',
    4 => 'FP4',
  ),
  '@FR' => 
  array (
    0 => 'Freelander I20',
    1 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    2 => '(MODECOM )?FreeTAB!!',
  ),
  '@FS' => 
  array (
    0 => 'FS403',
    1 => 'FS407',
    2 => 'FS451',
    3 => 'FS514',
    4 => 'FS8032',
  ),
  '@FT' => 
  array (
    0 => 'FTJ152A',
    1 => 'FTJ161B',
    2 => 'FTJ162E',
  ),
  '@FU' => 
  array (
    0 => 'full Android on Microsoft Windows, pad, pc, n*books',
    1 => 'Fusion Bolt',
    2 => 'FUSIONideos',
    3 => 'Fuel F2',
  ),
  '@FW' => 
  array (
    0 => 'FWS[0-9]{3,3}!!',
  ),
  '@FZ' => 
  array (
    0 => 'FZ-A1(A|B)!',
    1 => 'FZ-A2A',
    2 => 'FZ-B2(B|D)!',
    3 => 'FZ-N1',
    4 => 'FZ-T1',
    5 => 'FZ-X1',
  ),
  '@G ' => 
  array (
    0 => 'G Watch',
    1 => 'G Watch R',
  ),
  '@G0' => 
  array (
    0 => 'G0215D',
    1 => 'G0775',
  ),
  '@G1' => 
  array (
    0 => 'G1-715',
    1 => 'G1-725',
    2 => 'G100W',
    3 => 'G17(B5DV)',
    4 => 'G17(B7DV)',
    5 => 'G17h 3G??(K5E2)',
    6 => 'G17h 3G??(K6E2',
    7 => 'G17h 3G四核(K5E2)',
    8 => 'G17h 3G四核(K6E2)',
    9 => 'G17s 3G',
    10 => 'G17s 3G??(K5E5)',
    11 => 'G17s 3G四核(K5E5)',
    12 => 'G17s 3G四核(K5E4)',
    13 => 'G17s 3G四核(K5E3)',
    14 => 'G18 3G四核(C6B9)',
    15 => 'G18mini(C5B9)',
    16 => 'G18d 3G四核(D3A2)',
    17 => 'G18d mini(FV2C)',
  ),
  '@G2' => 
  array (
    0 => 'G2S',
    1 => 'G2SMNT',
  ),
  '@G3' => 
  array (
    0 => '(Moto)?G3$!',
    1 => '(Moto)?G3-TE$!',
    2 => 'G3SMNTS22',
    3 => 'G3SMNTS23',
  ),
  '@G6' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
  ),
  '@G7' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
  ),
  '@G9' => 
  array (
    0 => 'G9',
  ),
  '@GA' => 
  array (
    0 => '(Google )?Galaxy Nexus!',
    1 => 'Galaxy S4 Google Editon',
    2 => 'Garmin!!',
    3 => 'Galaxy!!',
  ),
  '@GE' => 
  array (
    0 => 'generic',
    1 => 'generic x86',
    2 => 'Gear Live',
    3 => 'Genius!!',
    4 => '(GP-|Geeksphone)!!',
    5 => 'Gemei!!',
    6 => 'GEM[0-9]{4,4}!!',
    7 => 'GEM-702L',
    8 => 'GEM-703L',
    9 => 'GETAWAY',
    10 => 'gemini',
    11 => 'General Mobile 4G',
    12 => 'General Mobile 4G Dual',
  ),
  '@GF' => 
  array (
    0 => 'GFIVE!!',
  ),
  '@GH' => 
  array (
    0 => 'Ghost',
    1 => 'GHIA!!',
  ),
  '@GI' => 
  array (
    0 => '(Gsmart|Gigabyte|Rio)!!',
    1 => 'Gigaset!!',
    2 => '(GIO-)?(GiONEE[- ])?A1$!',
    3 => '(GIO-)?(GiONEE[- ])?A9$!',
    4 => '(GIO-)?(GiONEE[- ])?C500!',
    5 => '(GIO-)?(GiONEE[- ])?C600!',
    6 => '(GIO-)?(GiONEE[- ])?C610!',
    7 => '(GIO-)?(GiONEE[- ])?C620!',
    8 => '(GIO-)?(GiONEE[- ])?C700!',
    9 => '(GIO-)?(GiONEE[- ])?C800!',
    10 => '(GIO-)?(GiONEE[- ])?Dream D1!',
    11 => '(GIO-)?(GiONEE[- ])?E3T!',
    12 => '(GIO-)?(GiONEE[- ])?E3$!',
    13 => '(GIO-)?(GiONEE[- ])?E5$!',
    14 => '(GIO-)?(GiONEE[- ])?E6mini!',
    15 => '(GIO-)?(GiONEE[- ])?E6$!',
    16 => '(GIO-)?(GiONEE[- ])?E6T$!',
    17 => '(GIO-)?(GiONEE[- ])?E7$!',
    18 => '(GIO-)?(GiONEE[- ])?F103!',
    19 => '(GIO-)?(GiONEE[- ])?F301!',
    20 => '(GIO-)?(GiONEE[- ])?GN5001!',
    21 => '(GIO-)?(GiONEE[- ])?GN5002!',
    22 => '(GIO-)?(GiONEE[- ])?GN8001!',
    23 => '(GIO-)?(GiONEE[- ])?GN9000!',
    24 => '(GIO-)?(GiONEE[- ])?GN9001!',
    25 => '(GIO-)?(GiONEE[- ])?GN9002!',
    26 => '(GIO-)?(GiONEE[- ])?GN9004!',
    27 => '(GIO-)?(GiONEE[- ])?GN9005!',
    28 => '(GIO-)?(GiONEE[- ])?GN9006!',
    29 => '(GIO-)?(GiONEE[- ])?GN9007!',
    30 => '(GIO-)?(GiONEE[- ])?GN9008!',
    31 => '(GIO-)?(GiONEE[- ])?GN9010!',
    32 => '(GIO-)?(GiONEE[- ])?GN100T!',
    33 => '(GIO-)?(GiONEE[- ])?GN100!',
    34 => '(GIO-)?(GiONEE[- ])?GN105!',
    35 => '(GIO-)?(GiONEE[- ])?GN106!',
    36 => '(GIO-)?(GiONEE[- ])?GN107!',
    37 => '(GIO-)?(GiONEE[- ])?GN108!',
    38 => '(GIO-)?(GiONEE[- ])?GN109!',
    39 => '(GIO-)?(GiONEE[- ])?GN135!',
    40 => '(GIO-)?(GiONEE[- ])?GN136!',
    41 => '(GIO-)?(GiONEE[- ])?GN137!',
    42 => '(GIO-)?(GiONEE[- ])?GN139!',
    43 => '(GIO-)?(GiONEE[- ])?GN150!',
    44 => '(GIO-)?(GiONEE[- ])?GN151!',
    45 => '(GIO-)?(GiONEE[- ])?GN152!',
    46 => '(GIO-)?(GiONEE[- ])?GN160T!',
    47 => '(GIO-)?(GiONEE[- ])?GN160!',
    48 => '(GIO-)?(GiONEE[- ])?GN168T!',
    49 => '(GIO-)?(GiONEE[- ])?GN170!',
    50 => '(GIO-)?(GiONEE[- ])?GN180!',
    51 => '(GIO-)?(GiONEE[- ])?GN181!',
    52 => '(GIO-)?(GiONEE[- ])?GN200!',
    53 => '(GIO-)?(GiONEE[- ])?GN205!',
    54 => '(GIO-)?(GiONEE[- ])?GN210!',
    55 => '(GIO-)?(GiONEE[- ])?GN305!',
    56 => '(GIO-)?(GiONEE[- ])?GN320!',
    57 => '(GIO-)?(GiONEE[- ])?GN360!',
    58 => '(GIO-)?(GiONEE[- ])?GN380!',
    59 => '(GIO-)?(GiONEE[- ])?GN600!',
    60 => '(GIO-)?(GiONEE[- ])?GN700T!',
    61 => '(GIO-)?(GiONEE[- ])?GN700W!',
    62 => '(GIO-)?(GiONEE[- ])?GN705T!',
    63 => '(GIO-)?(GiONEE[- ])?GN705W!',
    64 => '(GIO-)?(GiONEE[- ])?GN706L!',
    65 => '(GIO-)?(GiONEE[- ])?GN708T!',
    66 => '(GIO-)?(GiONEE[- ])?GN708W!',
    67 => '(GIO-)?(GiONEE[- ])?GN787!',
    68 => '(GIO-)?(GiONEE[- ])?GN800!',
    69 => '(GIO-)?(GiONEE[- ])?GN810!',
    70 => '(GIO-)?(GiONEE[- ])?GN818T!',
    71 => '(GIO-)?(GiONEE[- ])?GN858!',
    72 => '(GIO-)?(GiONEE[- ])?GN868!',
    73 => '(GIO-)?(GiONEE[- ])?GN878!',
    74 => 'Gionee M1',
    75 => 'Gionee M2',
    76 => 'Gionee-M2',
    77 => 'Gionee M3S',
    78 => 'Gionee M5',
    79 => '(GIO-)?(GiONEE[- ])?S101!',
    80 => 'GiONEE TD500',
    81 => '(GIO-)?(GiONEE[- ])?V182!',
    82 => '(GIO-)?(GiONEE[- ])?V185!',
    83 => '(GIO-)?(GiONEE[- ])?V188!',
    84 => '(GIO-)?(GiONEE[- ])?W800!',
    85 => '(GIO-)?(GiONEE[- ])?X817!',
    86 => '(GIO-)?(GiONEE[- ])?Ctrl V1!',
    87 => '(GIO-)?(GiONEE[- ])?Ctrl V2!',
    88 => '(GIO-)?(GiONEE[- ])?Ctrl V3!',
    89 => '(GIO-)?(GiONEE[- ])?Ctrl V4!',
    90 => '(GIO-)?(GiONEE[- ])?Ctrl V5!',
    91 => '(GIO-)?(GiONEE[- ])?Pioneer P1!',
    92 => '(GIO-)?(GiONEE[- ])?Pioneer P2!',
    93 => '(GIO-)?(GiONEE[- ])?Pioneer P3!',
    94 => '(GIO-)?(GiONEE[- ])?Pioneer P4!',
    95 => 'Gionee P3',
    96 => 'Gionee P4',
    97 => 'GIONEE P7',
    98 => 'GiONEE S7',
    99 => 'GIONEE S10',
    100 => '(GIO-)?(GiONEE[- ])?Gpad G1!',
    101 => '(GIO-)?(GiONEE[- ])?Gpad G2!',
    102 => '(GIO-)?(GiONEE[- ])?Gpad G3!',
    103 => 'GinDream/GinMagic',
  ),
  '@GK' => 
  array (
    0 => 'GK802',
  ),
  '@GL' => 
  array (
    0 => 'Glass 1',
    1 => 'GLADIATOR',
    2 => 'GLADIATOR-2',
    3 => 'GLADIATOR 3',
    4 => 'GLADIATOR 4',
    5 => 'Glory',
    6 => 'Glory 2',
    7 => 'Glory3',
    8 => 'GLORY 4',
    9 => 'GloryG1000',
    10 => 'GL-900',
    11 => 'GLX!!',
    12 => 'GL07S',
  ),
  '@GM' => 
  array (
    0 => 'GM190[0135]!',
    1 => 'GM191[01357]!',
    2 => 'Gm Discovery',
    3 => 'GM Discovery II',
    4 => 'GM Discovery II+',
    5 => 'GM FOX',
    6 => 'GM Ultimate Slim',
    7 => 'GM 5',
    8 => 'GM 5 d',
    9 => 'GM 5 Plus',
    10 => 'GM 5 Plus d',
    11 => 'GM 6',
    12 => 'GM 6 d',
    13 => 'GM 8 d',
    14 => 'GM8 go',
    15 => 'GM Discovery tab 8',
  ),
  '@GN' => 
  array (
    0 => '(GIO-)?(GiONEE[- ])?GN5001!',
    1 => '(GIO-)?(GiONEE[- ])?GN5002!',
    2 => '(GIO-)?(GiONEE[- ])?GN8001!',
    3 => '(GIO-)?(GiONEE[- ])?GN9000!',
    4 => '(GIO-)?(GiONEE[- ])?GN9001!',
    5 => '(GIO-)?(GiONEE[- ])?GN9002!',
    6 => '(GIO-)?(GiONEE[- ])?GN9004!',
    7 => '(GIO-)?(GiONEE[- ])?GN9005!',
    8 => '(GIO-)?(GiONEE[- ])?GN9006!',
    9 => '(GIO-)?(GiONEE[- ])?GN9007!',
    10 => '(GIO-)?(GiONEE[- ])?GN9008!',
    11 => '(GIO-)?(GiONEE[- ])?GN9010!',
    12 => '(GIO-)?(GiONEE[- ])?GN100T!',
    13 => '(GIO-)?(GiONEE[- ])?GN100!',
    14 => '(GIO-)?(GiONEE[- ])?GN105!',
    15 => '(GIO-)?(GiONEE[- ])?GN106!',
    16 => '(GIO-)?(GiONEE[- ])?GN107!',
    17 => '(GIO-)?(GiONEE[- ])?GN108!',
    18 => '(GIO-)?(GiONEE[- ])?GN109!',
    19 => '(GIO-)?(GiONEE[- ])?GN135!',
    20 => '(GIO-)?(GiONEE[- ])?GN136!',
    21 => '(GIO-)?(GiONEE[- ])?GN137!',
    22 => '(GIO-)?(GiONEE[- ])?GN139!',
    23 => '(GIO-)?(GiONEE[- ])?GN150!',
    24 => '(GIO-)?(GiONEE[- ])?GN151!',
    25 => '(GIO-)?(GiONEE[- ])?GN152!',
    26 => '(GIO-)?(GiONEE[- ])?GN160T!',
    27 => '(GIO-)?(GiONEE[- ])?GN160!',
    28 => '(GIO-)?(GiONEE[- ])?GN168T!',
    29 => '(GIO-)?(GiONEE[- ])?GN170!',
    30 => '(GIO-)?(GiONEE[- ])?GN180!',
    31 => '(GIO-)?(GiONEE[- ])?GN181!',
    32 => '(GIO-)?(GiONEE[- ])?GN200!',
    33 => '(GIO-)?(GiONEE[- ])?GN205!',
    34 => '(GIO-)?(GiONEE[- ])?GN210!',
    35 => '(GIO-)?(GiONEE[- ])?GN305!',
    36 => '(GIO-)?(GiONEE[- ])?GN320!',
    37 => '(GIO-)?(GiONEE[- ])?GN360!',
    38 => '(GIO-)?(GiONEE[- ])?GN380!',
    39 => '(GIO-)?(GiONEE[- ])?GN600!',
    40 => '(GIO-)?(GiONEE[- ])?GN700T!',
    41 => '(GIO-)?(GiONEE[- ])?GN700W!',
    42 => '(GIO-)?(GiONEE[- ])?GN705T!',
    43 => '(GIO-)?(GiONEE[- ])?GN705W!',
    44 => '(GIO-)?(GiONEE[- ])?GN706L!',
    45 => '(GIO-)?(GiONEE[- ])?GN708T!',
    46 => 'GN 708T',
    47 => '(GIO-)?(GiONEE[- ])?GN708W!',
    48 => '(GIO-)?(GiONEE[- ])?GN787!',
    49 => '(GIO-)?(GiONEE[- ])?GN800!',
    50 => '(GIO-)?(GiONEE[- ])?GN810!',
    51 => '(GIO-)?(GiONEE[- ])?GN818T!',
    52 => '(GIO-)?(GiONEE[- ])?GN858!',
    53 => '(GIO-)?(GiONEE[- ])?GN868!',
    54 => '(GIO-)?(GiONEE[- ])?GN878!',
    55 => 'Gnappo Ideos',
  ),
  '@GO' => 
  array (
    0 => 'google sdk',
    1 => 'Google Ion',
    2 => '(Google )?Nexus S!',
    3 => '(Google )?Galaxy Nexus!',
    4 => '(Google )?Nexus ?4!',
    5 => '(Google )?Nexus ?5!',
    6 => '(Google )?Nexus ?6P!',
    7 => '(Google )?Nexus ?6!',
    8 => '(Google )?Nexus ?7 2013!',
    9 => '(Google )?Nexus ?7!',
    10 => '(Google )?Nexus ?9!',
    11 => '(Google )?Nexus ?10!',
    12 => 'Google Pixel',
    13 => 'Google Pixel 2',
    14 => 'Google Pixel 2XL',
    15 => 'Google Chromebook Pixel (2015)',
    16 => 'google cube',
    17 => 'GOCL!!',
    18 => 'GO Live!!',
    19 => 'GOOPHONE!!',
    20 => 'GO !!',
    21 => 'GOA',
    22 => 'GO504',
    23 => 'GO778',
    24 => 'GO960',
    25 => 'GO963',
    26 => 'GO980',
    27 => 'GO984',
    28 => 'Gomobile GO1001',
  ),
  '@GP' => 
  array (
    0 => '(GP-|Geeksphone)!!',
    1 => 'GP-431',
    2 => 'GP-720',
    3 => 'GPLUS GN708W',
    4 => 'GPLUS N809',
    5 => '(GIO-)?(GiONEE[- ])?Gpad G1!',
    6 => '(GIO-)?(GiONEE[- ])?Gpad G2!',
    7 => '(GIO-)?(GiONEE[- ])?Gpad G3!',
    8 => 'Gpad G5',
  ),
  '@GR' => 
  array (
    0 => 'Grouper',
    1 => 'GR-TB!!',
    2 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    3 => 'greenridge',
    4 => 'Graphos A10',
    5 => '(ZTE )?(Grand|Mimosa)!!',
  ),
  '@GS' => 
  array (
    0 => 'GS01',
    1 => '(Gsmart|Gigabyte|Rio)!!',
    2 => 'GS110',
    3 => 'GS185',
    4 => 'GS190',
    5 => 'GS195',
    6 => 'GS270',
    7 => 'GS270 plus',
    8 => 'GS280',
    9 => 'GS290',
    10 => 'GS370 Plus',
    11 => 'GS02',
    12 => 'GS03',
  ),
  '@GT' => 
  array (
    0 => 'GT-I9505G',
    1 => 'GT-810',
    2 => 'GT-I7105',
    3 => 'GT-I9810',
    4 => 'GT-[HN][0-9]{4,4}!!',
    5 => 'GT-9000',
    6 => 'GT-[0-9]{4,4}!!',
    7 => 'GT-i9377',
    8 => 'GT-T9500',
    9 => 'GT540',
    10 => 'GT540f',
    11 => 'GTV100',
    12 => 'GT-B[0-9]{4,4}!!',
    13 => 'GT-I[0-9]{3,4}!!',
    14 => 'GT-N[0-9]{4,4}!!',
    15 => 'GT-P[0-9]{4,4}!!',
    16 => 'GT-S[0-9]{4,4}!!',
    17 => 'GT-T959!',
    18 => 'GT9100',
    19 => 'GTablet',
    20 => 'GtabComb',
  ),
  '@GU' => 
  array (
    0 => 'gucci',
  ),
  '@GV' => 
  array (
    0 => 'GVON 898',
  ),
  '@GW' => 
  array (
    0 => 'GW620',
  ),
  '@GX' => 
  array (
    0 => 'GX290',
    1 => 'GX1210V TV',
  ),
  '@H1' => 
  array (
    0 => 'H120',
  ),
  '@H2' => 
  array (
    0 => 'H2000+',
  ),
  '@H3' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@H6' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@H7' => 
  array (
    0 => 'H701',
    1 => 'H7100',
    2 => 'H7500+',
    3 => 'H710VL',
    4 => 'H715BL',
  ),
  '@H8' => 
  array (
    0 => 'H8 Life',
    1 => 'H866C',
    2 => 'H882L',
  ),
  '@H9' => 
  array (
    0 => 'H940',
    1 => 'H9500',
  ),
  '@HA' => 
  array (
    0 => 'HammerHead',
    1 => '(Haier[- ])?H[ETW]- ?[A-Z][0-9]!!',
    2 => 'Haier-W700',
    3 => 'Haier-SY0880',
    4 => 'Hammer Blade2 PRO',
    5 => 'Hammer Blade 3',
    6 => 'Hammer Energy',
    7 => 'Hammer Energy 2',
    8 => 'Hammer Energy 18x9',
    9 => 'Hammer Explorer',
    10 => 'Hammer Explorer Pro',
    11 => 'Hasee E50 S1',
    12 => 'Hasee H45 T2',
    13 => 'Hasee W50 T2',
    14 => 'Hasee-X50TS',
    15 => 'HannsComb',
    16 => 'Hannspadd',
    17 => 'Hayabusa',
    18 => 'HARRY',
  ),
  '@HB' => 
  array (
    0 => 'HB-100 DASAN',
    1 => 'HB-100 DASAN Networks, Inc.',
    2 => 'HB-1000',
  ),
  '@HD' => 
  array (
    0 => 'HD2',
    1 => 'HD2 LEO',
    2 => 'HD7  Pro',
    3 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    4 => 'HD190[0135]!',
    5 => 'HD191[0137]!',
  ),
  '@HE' => 
  array (
    0 => 'HEDY!!',
    1 => 'HERO H7500+',
    2 => 'Hero',
    3 => 'HERO CDMA',
    4 => 'HERO200',
  ),
  '@HI' => 
  array (
    0 => 'HiBook pro',
    1 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    2 => 'Himax Pure',
    3 => 'HIKe!!',
    4 => 'HILIVE H7',
    5 => '(HS-)?Hisense!!',
    6 => 'Hisense AD686G',
    7 => '(Hisense )?(LED[0-9]{2,2}(G|K|L|EC|XT)[0-9]{2,3})!',
    8 => 'Hisense Google TV TV',
    9 => 'hisense gx1200v',
    10 => '(Amaze|Hitech)!!',
    11 => 'HITO HT-3',
    12 => 'Hikari-iFrame!',
    13 => 'HIGHWAY',
    14 => 'HIGHWAY PURE',
    15 => 'HIGHWAY SIGNS',
  ),
  '@HK' => 
  array (
    0 => 'HKPHONE H8-3G',
  ),
  '@HL' => 
  array (
    0 => 'HLV-T!!',
    1 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@HM' => 
  array (
    0 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    1 => 'HMP8100 ATV 93',
    2 => 'HMP8100 ATV INT',
    3 => '(Xiaomi )?(Redmi|RedRice|HM)!!',
    4 => '(Xiaomi )?(Redmi|HM)[ \\-]?Note!!',
    5 => '(Xiaomi |HM)?20!!',
    6 => '(Xiaomi |HM)?21!!',
  ),
  '@HN' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@HO' => 
  array (
    0 => 'HOSIN!!',
    1 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    2 => 'Honor3c2G-T',
    3 => 'Honor Bee',
    4 => 'HOMESYNCT2WIFI',
  ),
  '@HP' => 
  array (
    0 => 'HP Chromebook x360 11 G1 EE',
    1 => '(HP|Slate)!!',
    2 => 'HP!!',
    3 => 'HP-TouchPad',
    4 => '(HP )?Touchpad!',
  ),
  '@HR' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@HS' => 
  array (
    0 => 'HS-[0-9]{1,2}DTB!!',
    1 => 'HS-[EFHITUX][0-9]!!',
    2 => 'HS-E[GT][0-9]{3,3}!!',
    3 => '(HS-)?Hisense!!',
    4 => 'HS-EG98',
    5 => 'HS U978',
  ),
  '@HT' => 
  array (
    0 => '(HTC )?Nexus ?One!',
    1 => 'HTC One',
    2 => 'HT7 Pro',
    3 => 'HT17Pro',
    4 => 'HT20Pro',
    5 => 'HTC!!',
    6 => '(HTC|PCD|USCC)?ADR[0-9]{4,4}!!',
    7 => '(HTC )?HT[LV][0-9]{2,2}!!',
    8 => 'HTX21',
    9 => 'HTC Dream',
    10 => 'HTC Dream G1',
    11 => 'HTC Dream SparksMod ',
    12 => 'HTC Vision',
    13 => 'HTC T-Mobile myTouch 3G Slide',
    14 => 'HTC my ?Touch 3G Slide!',
    15 => 'HTC Glacier',
    16 => 'HTC Panache',
    17 => 'HTC My ?Touch ?4G$!',
    18 => 'HTC S910m',
    19 => 'HTC myTouch 4G Slide',
    20 => 'HTC Doubleshot',
  ),
  '@HU' => 
  array (
    0 => 'HUAWEI WATCH',
    1 => 'Huawei MediaPad',
    2 => 'Huawei OsiMood MediaPad',
    3 => 'HUAWEI MediaPad M1 8.0',
    4 => 'Huawei MediaPad T1 7.0 3G',
    5 => 'Huawei MediaPad T1 8.0 4G',
    6 => 'Huawei MediaPad T1 10 4G',
    7 => '(Huawei )?[MSTX](1|2|7|8|10)\\-[A-Z0-9][0-9]{2,2}!!',
    8 => 'HUAWEI GEM-703L',
    9 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    10 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    11 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    12 => '(HW-|HUAWEI )?(BLL|CHC|TAG|KII)!!',
    13 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    14 => 'HUAWEI A168-(AL10|DL09|L09|L29)!',
    15 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    16 => '(HW-|HUAWEI )?CAZ-(AL10|TL10|TL20)!',
    17 => '(HW-|HUAWEI )?CAN-(L01|L11|L12)!',
    18 => '(HW-|HUAWEI )?MLA-(L01|L03|L11|L12|L13)!',
    19 => '(HW-|HUAWEI )?DIG-(AL00|L01|L03|L21|TL10)!',
    20 => '(HW-|HUAWEI )?PIC-(LX9)!',
    21 => '(HW-|HUAWEI )?BAC-(AL00|L03|L21|L22|TL00)!',
    22 => '(HW-|HUAWEI )?HWI-(AL00|TL00)!',
    23 => '(HW-|HUAWEI )?PAR-(AL00|LX1|LX1M|LX9|TL00)!',
    24 => '(HW-|HUAWEI )?INE-(AL00|LX1|LX1r|LX2||LX2r|TL00)!',
    25 => '(HW-|HUAWEI )?MAR-(AL00|LX1A|LX1M|LX2|LX3A|TL00)!',
    26 => '(HW-|HUAWEI )?MLA-(AL00|AL10)!',
    27 => '(HW-|HUAWEI )?MLA-(TL00|TL10|UL00)!',
    28 => 'Huawei P7 mini',
    29 => 'Huawei P8max',
    30 => '(Huawei|Ascend|HW-)!!',
    31 => 'HUAWEI Honor 3c w',
    32 => 'Huawei S7',
    33 => 'Hudl HT7S3',
    34 => 'Hudl 2',
    35 => 'HUAWEI U8686',
    36 => 'Huawei 858',
  ),
  '@HW' => 
  array (
    0 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    1 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    2 => '(HW-|HUAWEI )?(BLL|CHC|TAG|KII)!!',
    3 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    4 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    5 => '(HW-|HUAWEI )?CAZ-(AL10|TL10|TL20)!',
    6 => '(HW-|HUAWEI )?CAN-(L01|L11|L12)!',
    7 => '(HW-|HUAWEI )?MLA-(L01|L03|L11|L12|L13)!',
    8 => '(HW-|HUAWEI )?DIG-(AL00|L01|L03|L21|TL10)!',
    9 => '(HW-|HUAWEI )?PIC-(LX9)!',
    10 => '(HW-|HUAWEI )?BAC-(AL00|L03|L21|L22|TL00)!',
    11 => '(HW-|HUAWEI )?HWI-(AL00|TL00)!',
    12 => '(HW-|HUAWEI )?PAR-(AL00|LX1|LX1M|LX9|TL00)!',
    13 => '(HW-|HUAWEI )?INE-(AL00|LX1|LX1r|LX2||LX2r|TL00)!',
    14 => '(HW-|HUAWEI )?MAR-(AL00|LX1A|LX1M|LX2|LX3A|TL00)!',
    15 => '(HW-|HUAWEI )?MLA-(AL00|AL10)!',
    16 => '(HW-|HUAWEI )?MLA-(TL00|TL10|UL00)!',
    17 => '(Huawei|Ascend|HW-)!!',
    18 => 'hwu8812D',
    19 => 'HW-01E',
    20 => 'HW-01K',
    21 => 'HW-03E',
    22 => 'HWT31',
    23 => 'HWV32',
  ),
  '@HY' => 
  array (
    0 => 'HY5001',
    1 => 'HYUNDAI!!',
    2 => 'Hydro',
    3 => 'Hydro PLUS',
    4 => 'HYPER X BLADE',
  ),
  '@I ' => 
  array (
    0 => 'I 7520',
  ),
  '@I-' => 
  array (
    0 => 'i-Joy i-Call',
    1 => 'i-Call 300',
    2 => 'i-Call 300v2',
    3 => 'i-Call 504',
    4 => '(i-mobile|i-style|IQ)!!',
  ),
  '@I0' => 
  array (
    0 => 'I01WDX',
    1 => 'I001DC',
    2 => 'I001DE',
    3 => 'I002D',
    4 => 'I003D',
    5 => 'I003DD',
    6 => 'I004D',
    7 => 'I005D',
    8 => 'I005DA',
    9 => 'I006D',
    10 => 'I01WD',
  ),
  '@I1' => 
  array (
    0 => 'I110',
    1 => 'i1',
  ),
  '@I3' => 
  array (
    0 => 'i3000',
  ),
  '@I5' => 
  array (
    0 => 'i5300',
    1 => 'i5350',
  ),
  '@I6' => 
  array (
    0 => 'i6000',
  ),
  '@I7' => 
  array (
    0 => 'I7500',
  ),
  '@I8' => 
  array (
    0 => 'i8400',
    1 => 'i8450',
    2 => 'i803w',
    3 => 'I897',
  ),
  '@I9' => 
  array (
    0 => 'I9220',
    1 => 'i9400',
    2 => 'i9430',
    3 => 'i9480',
    4 => 'i9500',
    5 => 'i9570',
    6 => 'I9000',
    7 => 'I9300 Galaxy SIII ',
    8 => 'I9300',
  ),
  '@IB' => 
  array (
    0 => '(iBall )?Andi!!',
    1 => 'iBall!!',
    2 => '(Aura|iberry|AUXUS)!!',
  ),
  '@IC' => 
  array (
    0 => 'ICONIA!!',
    1 => 'IconBit NetTab Thor Mini',
  ),
  '@ID' => 
  array (
    0 => '(Digma )?iD[jmnsx][DQ]?[0-9]*!!',
    1 => 'IDEOS!!',
    2 => 'IdeaPad A1',
    3 => 'IdeaPadA10',
    4 => 'Ideapad K1!',
    5 => 'Ideapad S10-3T',
    6 => '(Lenovo ?)?(IdeaTab ?)?[AB][0-9]{4,4}!!',
    7 => '(Lenovo ?)?(IdeaTab ?)?[KSV][0-9]{4,4}!!',
    8 => 'idea ?tv!!',
  ),
  '@IF' => 
  array (
    0 => 'ifive!!',
  ),
  '@IG' => 
  array (
    0 => 'IGGY',
  ),
  '@IL' => 
  array (
    0 => '(Ilium )?Avvio!!',
    1 => 'ILT-MX100',
    2 => '(Lanix )?(Ilium|llium)!!',
  ),
  '@IM' => 
  array (
    0 => 'imx50!',
    1 => 'imx51!',
    2 => 'imx53!',
    3 => 'imx6q!',
    4 => 'IMO!!',
    5 => 'Im(Smart|PAD)!!',
    6 => '(SKY )?IM[- ][A-Z][0-9]{3,3}!!',
    7 => 'IM-100K',
    8 => 'IM-100S',
  ),
  '@IN' => 
  array (
    0 => '(Explay|X-tremer|ActiveD|Informer|Surfer)!!',
    1 => 'INSIGNIA 5',
    2 => 'INSIGNIA 5X',
    3 => 'Inspire HD',
    4 => 'Incredible',
    5 => 'Incredible 2',
    6 => 'Incredible ?S!',
    7 => 'Incredible ?2!',
    8 => 'Incredible 4G LTE',
    9 => 'Inspire 4G',
    10 => '(HW-|HUAWEI )?INE-(AL00|LX1|LX1r|LX2||LX2r|TL00)!',
    11 => 'INFOBAR!!',
    12 => 'Infinix!!',
    13 => 'IN[0-9]{3,3}!!',
    14 => 'Infocus!!',
    15 => 'INHON PAPILIO G1',
    16 => 'intki E86',
    17 => 'Innos!!',
    18 => '(Intex )?Aqua!!',
    19 => '(Intex )?Cloud!!',
    20 => 'INTEX IRIS-II',
    21 => 'INQ Cloud Touch',
    22 => 'INQ Mayfair (EU)',
    23 => '(iNote|itel)!!',
    24 => 'Infinity',
    25 => 'IN201[01357]!',
    26 => 'IN202[0135]!',
    27 => 'Internet TV',
    28 => 'Internet TV Box',
  ),
  '@IO' => 
  array (
    0 => 'iOCEAN!!',
  ),
  '@IP' => 
  array (
    0 => 'iP977',
    1 => 'IPRO i5S',
  ),
  '@IQ' => 
  array (
    0 => '(Fly )?IQ ?[0-9]{3,4}!!',
    1 => 'iQ(Talk|Tab)!!',
    2 => 'iQ7 8GB',
    3 => '(i-mobile|i-style|IQ)!!',
  ),
  '@IR' => 
  array (
    0 => 'Iris!!',
  ),
  '@IS' => 
  array (
    0 => 'IS11CA',
    1 => 'ISW?[0-9]{2,2}F!!',
    2 => 'ISW[0-9]{2,2}HT!!',
    3 => 'IS530',
    4 => 'ISW11K',
    5 => 'IS11LG',
    6 => 'ISW11M',
    7 => 'IS12M',
    8 => 'IS11N',
    9 => 'IS06',
    10 => 'IS11PT',
    11 => 'IS703C',
    12 => 'IS801C',
    13 => 'ISW11SC',
    14 => 'IS0[0-9]!!',
    15 => 'ISW?1[0-9]SH!!',
    16 => 'IS[0-9]{2,2}S!!',
    17 => 'IS04',
    18 => 'IS11T',
  ),
  '@IT' => 
  array (
    0 => '(iNote|itel)!!',
    1 => 'ITP-XD10W',
    2 => 'ITP-XD80L',
    3 => 'ITP-XD97W',
  ),
  '@IU' => 
  array (
    0 => 'IUNI U810',
  ),
  '@IV' => 
  array (
    0 => 'IVIO!!',
    1 => 'IV2201',
  ),
  '@IW' => 
  array (
    0 => 'iwoo i900',
  ),
  '@IX' => 
  array (
    0 => 'iX101T1',
    1 => 'iX101T1-2G',
    2 => 'iX101T1-XS',
  ),
  '@JA' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    1 => 'Jaguar3',
    2 => 'Jaguar7',
  ),
  '@JC' => 
  array (
    0 => 'JC-S9220',
    1 => 'JC-A[0-9]{3,3}!!',
    2 => 'JC-KSP8000',
  ),
  '@JD' => 
  array (
    0 => 'JDN-W09',
    1 => 'JDN2-AL00HN',
    2 => 'JDN2-W09HN',
  ),
  '@JE' => 
  array (
    0 => 'Jelly2',
  ),
  '@JI' => 
  array (
    0 => '(JIAYU|JY)!!',
    1 => 'JIMMY',
  ),
  '@JJ' => 
  array (
    0 => 'JJ5S+',
  ),
  '@JK' => 
  array (
    0 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
  ),
  '@JM' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@JN' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
  ),
  '@JO' => 
  array (
    0 => 'Jolla',
    1 => 'Joy',
    2 => 'JOIN',
  ),
  '@JS' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@JT' => 
  array (
    0 => 'JT SmartPC02',
    1 => 'JT-Smart PC01',
    2 => 'JT-H580VT',
    3 => 'JT-H581VT',
    4 => 'JT-B1',
  ),
  '@JX' => 
  array (
    0 => 'JXD!!',
  ),
  '@JY' => 
  array (
    0 => '(JIAYU|JY)!!',
  ),
  '@K0' => 
  array (
    0 => 'K00C',
    1 => 'K010',
    2 => 'K010E',
    3 => 'K018',
    4 => 'K014',
    5 => 'K01B',
    6 => 'K00G',
    7 => 'K00E',
    8 => 'K00Y',
    9 => 'K00Z',
    10 => 'K012',
    11 => 'K019',
    12 => 'K01F',
    13 => 'K01N',
    14 => 'K01Q',
    15 => 'K016',
    16 => 'K01E',
    17 => 'K00X',
    18 => 'K00R',
    19 => 'K007',
    20 => 'K013',
    21 => 'K013C',
    22 => 'K017',
    23 => 'K01A',
    24 => 'K012 2',
    25 => 'K01U',
    26 => 'K00S',
    27 => 'K00U',
    28 => 'K011',
    29 => 'K015',
    30 => 'K01H',
    31 => 'K00L',
    32 => 'K00F',
    33 => 'K080',
  ),
  '@K1' => 
  array (
    0 => 'K11',
    1 => 'K1',
    2 => 'K10000 Pro',
  ),
  '@K3' => 
  array (
    0 => 'K3',
    1 => 'K30-T',
    2 => 'K30-W',
    3 => 'K3 Note',
  ),
  '@K6' => 
  array (
    0 => 'K6000 Pro',
  ),
  '@K8' => 
  array (
    0 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
  ),
  '@K9' => 
  array (
    0 => 'K9 Smart 4G',
  ),
  '@KA' => 
  array (
    0 => 'Karbonn!!',
    1 => '(Karbonn|Titanium)!!',
    2 => 'Karbonn Aura 9',
    3 => '(KAZAM|Thunder|Tornado|Trooper)!!',
    4 => '(KATBL|Kogan|Agora)!!',
    5 => 'Kavak Y625-U03',
  ),
  '@KB' => 
  array (
    0 => 'KB901',
    1 => 'KB200[01357]!',
  ),
  '@KC' => 
  array (
    0 => 'KC-01',
    1 => 'KC-S301AE',
    2 => 'KC-S701',
    3 => 'KCP01K',
  ),
  '@KE' => 
  array (
    0 => 'KENEKSI!!',
    1 => 'kenzo',
  ),
  '@KF' => 
  array (
    0 => 'KFOTE?!',
    1 => 'KFTT',
    2 => 'KFJW(I|A)!',
    3 => 'KFJMWI',
    4 => 'KFSOWI',
    5 => 'KFTHW(I|A)!',
    6 => 'KFAPW(I|A)!',
    7 => 'KFARWI',
    8 => 'KFASWI',
    9 => 'KFSAW(I|A)!',
    10 => 'KFFOWI',
    11 => 'KFMEWI',
    12 => 'KFTBWI',
    13 => 'KFAUWI',
    14 => 'KFGIWI',
    15 => 'KFDOWI',
    16 => 'KFSUWI',
    17 => 'KFMAWI',
    18 => 'KFKAWI',
    19 => 'KFMUWI',
  ),
  '@KI' => 
  array (
    0 => 'Kindle Fire',
    1 => 'KINGKONG 5 Pro',
    2 => 'KINGKONG MINI2',
    3 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    4 => '(HW-|HUAWEI )?(BLL|CHC|TAG|KII)!!',
    5 => 'KIOTO 793',
    6 => 'KITE',
    7 => '(ZTE )?Kis!!',
  ),
  '@KM' => 
  array (
    0 => '(EV|KM)-[ES][0-9]{3,3}!!',
  ),
  '@KN' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@KO' => 
  array (
    0 => 'Konka Amber3',
    1 => 'KOB-(L09|W09)!',
    2 => 'KOB2-(W09)!',
    3 => '(KATBL|Kogan|Agora)!!',
    4 => 'KOMU!!',
    5 => 'KONKA!!',
    6 => 'koobee-T550',
    7 => 'Koobe!!',
    8 => 'KORIDY H15',
    9 => 'KORIDY H16',
  ),
  '@KP' => 
  array (
    0 => 'KPT!!',
    1 => 'KPN!!',
  ),
  '@KS' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    1 => 'KSP8000',
  ),
  '@KT' => 
  array (
    0 => 'KT-101-A',
  ),
  '@KU' => 
  array (
    0 => 'Kurio!!',
    1 => 'KU9500',
    2 => 'KUNO4',
    3 => 'KUNO 4+',
  ),
  '@KY' => 
  array (
    0 => 'KYV33',
    1 => 'Kyobo mirasol eReader',
    2 => 'KYL[0-9]{2,2}!!',
    3 => '(USCC-|KYOCERA-)?E[0-9]{4,4}!!',
    4 => '(USCC-|KYOCERA-)?C[0-9]{4,4}!!',
    5 => 'KY[FLTYV][0-9]{2,2}!!',
    6 => 'Kyivstar!!',
  ),
  '@L' => 
  array (
    0 => '(Hisense )?(LED[0-9]{2,2}(G|K|L|EC|XT)[0-9]{2,3})!',
  ),
  '@L-' => 
  array (
    0 => 'L-0[0-9][A-Z]!!',
  ),
  '@L3' => 
  array (
    0 => 'L3',
  ),
  '@L9' => 
  array (
    0 => 'l97D',
  ),
  '@LA' => 
  array (
    0 => 'LA-!!',
    1 => 'Land Rover Explore',
    2 => 'Land Rover Explore R',
    3 => '(Lanix )?(Ilium|llium)!!',
    4 => 'Lark!!',
    5 => 'LAVA iris 351',
    6 => 'LAVA iRIS 504q',
    7 => 'LAVA A1',
    8 => 'Lava A89',
    9 => 'LAVA R1',
    10 => 'LAVA R1s',
    11 => 'LAVA S12',
    12 => 'LAVA V5',
    13 => 'LAP250U',
    14 => 'LAP255U',
    15 => 'LaVieTab!!',
    16 => 'land',
    17 => 'LA-I Dual core',
    18 => 'LA-M3',
    19 => 'LA1-L',
    20 => 'LA2-E',
    21 => 'LA2-E1',
    22 => 'LA2-L',
    23 => 'LA2-T',
    24 => 'LA2-T1',
    25 => 'LA2-S',
    26 => 'LA2-SN',
    27 => 'LA2-W',
    28 => 'LA2-W1',
    29 => 'LA3-W',
    30 => 'LA3S',
    31 => 'LA5-W',
  ),
  '@LC' => 
  array (
    0 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
    1 => 'LC-Ux30US',
    2 => 'LC-[0-9]{2,2}((LE|UE)[0-9]{1,3}[A-Z])!',
    3 => 'LC-((A|S|LX)[0-9]{1,3}[A-Z])!',
    4 => 'LCD-((S|V|LX|UF)[0-9]{1,3}[A-Z])!',
  ),
  '@LD' => 
  array (
    0 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    1 => 'LDK-ICK v1.4',
  ),
  '@LE' => 
  array (
    0 => '(LE[0-9]{2,2}[BMU][0-9]{4,4}[A-Z]+)!',
    1 => '(Hisense )?(LED[0-9]{2,2}(G|K|L|EC|XT)[0-9]{2,3})!',
    2 => 'Legend',
    3 => 'LEAGOOLG13',
    4 => 'Le 1S',
    5 => 'Le 1 Pro',
    6 => 'Le 2',
    7 => 'Le 2 Pro',
    8 => 'Le Max',
    9 => 'LEX626',
    10 => 'LEX720',
    11 => 'LEX820',
    12 => 'Le X500',
    13 => 'Le X501',
    14 => 'Le X506',
    15 => 'Le X507',
    16 => 'Le X509',
    17 => 'Le X520',
    18 => 'Le X522',
    19 => 'Le X526',
    20 => 'Le X527',
    21 => 'Le X528',
    22 => 'Le X620',
    23 => 'Le X621',
    24 => 'Le X622',
    25 => 'Le X625',
    26 => 'Le X820',
    27 => 'Le X821',
    28 => 'Le X822',
    29 => 'Le X829',
    30 => 'Le X829M',
    31 => 'Le X920',
    32 => 'LETV Letv X500',
    33 => 'LeTv 1s',
    34 => 'Letv X500',
    35 => 'Letv X501',
    36 => 'Letv X502',
    37 => 'Letv X507',
    38 => 'Letv X520',
    39 => 'Letv X600',
    40 => 'Letv X900',
    41 => 'Letv X910',
    42 => 'Letv Max4-70',
    43 => 'Letv C1S',
    44 => 'Letv U2',
    45 => 'Letv S40 Air',
    46 => 'Letv S40 Air L',
    47 => 'Letv S50 Air',
    48 => 'Letv X3-40',
    49 => 'Letv X3-50 UHD',
    50 => 'Letv X3-55',
    51 => 'Letv X3-55 Pro',
    52 => 'Letv X3-65',
    53 => 'LeTVX60',
    54 => 'leepoo!!',
    55 => 'LEMON P7',
    56 => 'Lemon S3',
    57 => 'Lemon S8',
    58 => 'Lenco!!',
    59 => 'LENCM900HZ',
    60 => 'Lenovo A1-32AB0',
    61 => 'Lenovo A1-32AJ0',
    62 => 'Lenovo IdeaPad Yoga 11S',
    63 => 'Lenovo IdeaPad Yoga 13',
    64 => '(Lenovo ?)?(IdeaTab ?)?[AB][0-9]{4,4}!!',
    65 => 'Lenovo YB1-X90L',
    66 => 'Lenovo YB1-X90F',
    67 => 'Lenovo YB-Q501F',
    68 => 'Lenovo YT3-X50F',
    69 => 'Lenovo YT3-X50L',
    70 => 'Lenovo YT3-850F',
    71 => 'Lenovo YT-J706F',
    72 => 'Lenovo YT-X703F',
    73 => 'Lenovo YT-X705F',
    74 => 'Lenovo YT3-X90F',
    75 => 'Lenovo YT3-X90L',
    76 => '(Lenovo )?(Tab ?)?(2 ?)?[AS](7|8|10)!!',
    77 => 'Lenovo TB-7304F',
    78 => 'Lenovo TB-7305F',
    79 => 'Lenovo TB-7305X',
    80 => 'Lenovo TB-8304F1',
    81 => 'Lenovo TB-8505FS',
    82 => 'Lenovo TB-8704X',
    83 => 'Lenovo TB-8705F',
    84 => 'Lenovo TB-J606F',
    85 => 'Lenovo TB-J606L',
    86 => 'Lenovo TB-J607Z',
    87 => 'Lenovo TB-J616F',
    88 => 'Lenovo TB-J706F',
    89 => 'Lenovo TB-J716F',
    90 => 'Lenovo TB-X104F',
    91 => 'Lenovo TB-X304L',
    92 => 'Lenovo TB-X306F',
    93 => 'Lenovo TB-X306X',
    94 => 'Lenovo TB-X505F',
    95 => 'Lenovo TB-X605F',
    96 => 'Lenovo TB-X605L',
    97 => 'Lenovo TB-X606F',
    98 => 'Lenovo TB-X606X',
    99 => 'Lenovo TB-X6C6F',
    100 => 'Lenovo TB-X103F',
    101 => 'Lenovo TB2-X30F',
    102 => 'Lenovo TB2-X30L',
    103 => 'Lenovo TB3-730X',
    104 => 'Lenovo TB3-710F',
    105 => 'Lenovo TB3-710I',
    106 => 'Lenovo TB-7703X',
    107 => 'Lenovo TB3-850F',
    108 => 'Lenovo TB3-850M',
    109 => 'Lenovo TB3-X70F',
    110 => 'Lenovo TB3-X70L',
    111 => 'Lenovo TB-8504F',
    112 => 'Lenovo TB-8504X',
    113 => 'Lenovo TB-8505F',
    114 => 'Lenovo TB-8703F',
    115 => 'Lenovo TB-8704F',
    116 => 'Lenovo TB-X304F',
    117 => 'Lenovo TB-X704F',
    118 => 'Lenovo TB-X704L',
    119 => 'Lenovo TB-X705F',
    120 => 'Lenovo TB-X705L',
    121 => '(Lenovo ?)?(IdeaTab ?)?[KSV][0-9]{4,4}!!',
    122 => 'Lenovo Pad A4',
    123 => 'Lenovo',
    124 => 'lepad 001b',
    125 => 'lepad 001n',
    126 => '(Lenovo |Lephone )?3GC101!',
    127 => '(Lenovo |Lephone )?3GW100!',
    128 => '(Lenovo |Lephone )?3GW101!',
    129 => '(Lenovo )?S1[- ]37AH0!',
    130 => '(Lenovo )?S2[- ]38A(H0|T0)!',
    131 => 'Lenovo!!',
    132 => 'LenovoTV 40S9',
    133 => 'LenovoTV 50S52',
    134 => 'Lenovo Z2',
    135 => '(BLF-)?lephone!!',
    136 => 'Le Pan Mini',
    137 => 'Le Pan TC802A',
    138 => 'Le Pan TC1010',
    139 => 'Le Pan TC1020',
    140 => 'LePanII',
    141 => 'Le Pan S',
    142 => 'LE2101',
    143 => 'LE211[0135]!',
    144 => 'LE212[01357]!',
    145 => 'LENNY',
    146 => 'LENNY2',
    147 => 'LENNY3',
    148 => 'Lenny4',
  ),
  '@LG' => 
  array (
    0 => 'LGE-lap crespo',
    1 => 'LGE-lap maguro',
    2 => 'LGE-maguro cappuccino',
    3 => 'LGE-lap mako',
    4 => 'LG-Nexus5',
    5 => 'LG-Watch Urbane',
    6 => 'LG-AN160',
    7 => 'LG-AK495',
    8 => 'LG-?AS[0-9]{3,3}!!',
    9 => 'LG-?C[0-9]{3,3}!!',
    10 => 'LG-CX670',
    11 => 'LG-?D[0-9]{3,3}!!',
    12 => 'LG-DS1203',
    13 => 'LG-?E[0-9]{3,3}!!',
    14 => 'LG-F[0-9]{3,3}!!',
    15 => 'LG-FL40L!',
    16 => 'LG-GT540!',
    17 => 'LG-GW620!',
    18 => 'LG-H[0-9]{3,3}!!',
    19 => 'LG-K[0-9]{3,3}!!',
    20 => 'LG-KH5200',
    21 => 'LG-KS1024',
    22 => 'LG-KU[0-9]{4,4}!!',
    23 => 'LG-?L[0-9]{2,3}[A-Z]!!',
    24 => 'LG-LG[0-9]{3,3}!!',
    25 => 'LGLK430',
    26 => 'LG-LK460',
    27 => 'LG-?LS[0-9]{3,3}!!',
    28 => 'LG-LU[0-9]{4,4}!!',
    29 => 'LG-LW690',
    30 => 'LG-LW770',
    31 => 'LG-M[0-9]{3,3}!!',
    32 => 'LG-?MS[0-9]{3,3}!!',
    33 => 'LGMP260',
    34 => 'LG-P[0-9]{3,3}!!',
    35 => 'LG-Q710!',
    36 => 'LG-RS988!',
    37 => 'LG-SP[0-9]{3,3}!!',
    38 => 'LG-SU[0-9]{3,3}!!',
    39 => 'LG-T480!',
    40 => 'LG-T540!',
    41 => 'LG-TP260',
    42 => 'LG-TP450',
    43 => 'LG-SU-760',
    44 => 'LGUK410',
    45 => 'LG-UK495',
    46 => 'LGUK750',
    47 => 'LGUK932',
    48 => 'LG-UN350',
    49 => 'LG-?US[0-9]{3,3}!!',
    50 => 'LG-V[0-9]{3,3}!!',
    51 => 'LG-VK[0-9]{3,3}!!',
    52 => 'LG-VN[0-9]{3,3}!!',
    53 => 'LG-VM[0-9]{3,3}!!',
    54 => 'LG-VS[0-9]{3,3}!!',
    55 => 'LG-X[0-9]{3,3}!!',
    56 => 'LGM-[GKVX][0-9]{3,3}!!',
    57 => 'LGMP450',
    58 => 'lge-F160!',
    59 => 'LGE-E988!',
    60 => 'lgp-970',
    61 => 'LG[LSTV][0-9]{2,2}!!',
    62 => 'LG!!',
    63 => 'LG-Google TV!!',
    64 => 'LG-ANDROID MINI BOX',
    65 => 'LG-P999',
    66 => 'LG-E739',
    67 => 'LG-C800',
  ),
  '@LI' => 
  array (
    0 => 'Liquid!!',
    1 => '(BLU|DASH|LIFE|NEO|STUDIO|VIVO)!!',
    2 => 'Liberty',
    3 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    4 => '(MEDION|(MD )?LIFETAB)!!',
    5 => 'Life',
    6 => 'Liberty Tab G100',
    7 => 'Live ?With ?Walkman!',
    8 => 'libra',
    9 => 'lithium',
    10 => 'Light Tab',
    11 => 'Light Tab 2',
    12 => 'Light Tab 2W',
  ),
  '@LL' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    1 => '(Lanix )?(Ilium|llium)!!',
  ),
  '@LM' => 
  array (
    0 => 'LM-F100!',
    1 => 'LM-G710!',
    2 => 'LM-G715!',
    3 => 'LM-G810!',
    4 => 'LM-G820!',
    5 => 'LM-G850!',
    6 => 'LM-G900!',
    7 => 'LM-G910!',
    8 => 'LM-K200!',
    9 => 'LM-K410!',
    10 => 'LM-K420!',
    11 => 'LM-K510!',
    12 => 'LM-K520!',
    13 => 'LM-Q610!',
    14 => 'LM-Q617!',
    15 => 'LM-Q630!',
    16 => 'LM-Q710!',
    17 => 'LM-Q720!',
    18 => 'LM-Q725!',
    19 => 'LM-Q815!',
    20 => 'LM-Q850!',
    21 => 'LM-Q910!',
    22 => 'LM-Q925!',
    23 => 'LM-V350!',
    24 => 'LM-V40[59]!',
    25 => 'LM-V450!',
    26 => 'LM-V500!',
    27 => 'LM-V510!',
    28 => 'LM-V600!',
    29 => 'LM-X120!',
    30 => 'LM-X210!',
    31 => 'LM-X212!',
    32 => 'LM-X220!',
    33 => 'LM-X320!',
    34 => 'LM-X410!',
    35 => 'LM-X415!',
    36 => 'LM-X420!',
    37 => 'LM-X430!',
    38 => 'LM-X510!',
    39 => 'LM-X520!',
    40 => 'LM-X525!',
    41 => 'LM-X540!',
    42 => 'LM-X625!',
    43 => 'LM-Y110!',
  ),
  '@LO' => 
  array (
    0 => 'LogicPD Zoom2',
    1 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    2 => 'Loox',
  ),
  '@LR' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@LS' => 
  array (
    0 => 'LS670',
    1 => 'LS740',
    2 => 'LS-4001',
    3 => 'LS-4002',
    4 => 'LS-4003',
    5 => 'LS-4004',
    6 => 'LS-4005',
    7 => 'LS-4006',
    8 => 'LS-4008',
    9 => 'LS-4503',
    10 => 'LS-4505',
    11 => 'LS-5002',
    12 => 'LS-5004',
    13 => 'LS-5005',
    14 => 'LS-5006',
    15 => 'LS-5007',
    16 => 'LS-5008',
    17 => 'LS-5009',
    18 => 'LS-5010',
    19 => 'LS-5013',
    20 => 'LS-5014',
    21 => 'LS-5015',
    22 => 'LS-5016',
    23 => 'LS-5017',
    24 => 'LS-5018',
    25 => 'LS-5020',
    26 => 'LS-5021',
    27 => 'LS-5201',
    28 => 'LS-5501',
    29 => 'LS-5502',
    30 => 'LS-5503',
    31 => 'LS-5504',
    32 => 'LS-5505',
    33 => 'LS-5506',
    34 => 'LS-5507',
    35 => 'LS-6001',
  ),
  '@LT' => 
  array (
    0 => 'LT[0-9]{4,4}!!',
    1 => 'LTB-HS',
    2 => 'LT-TLA',
    3 => 'LT-NA7',
    4 => 'LT-NA7F',
    5 => '(SNM\\-)?LT[0-9]{2,2}[a-z]?!!',
  ),
  '@LU' => 
  array (
    0 => 'LUXURY',
    1 => 'LUXURY 3',
    2 => 'LUXURY 4',
    3 => 'LUXURY 5',
    4 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    5 => 'Lucky Ultra Sonic U8650',
    6 => 'LU2300',
    7 => 'Lumia800',
    8 => 'Lumia 900',
    9 => 'Luna TAB07-920N',
    10 => 'Luna TAB07-100',
    11 => 'Luna TAB07-101',
    12 => 'Luna TAB274',
    13 => 'Luna TAB374',
    14 => 'Luna TAB474',
    15 => 'Luna TAB10-150',
    16 => 'Luno',
  ),
  '@LY' => 
  array (
    0 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    1 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    2 => 'LYF LS-4006',
    3 => 'LYF LS-5009',
  ),
  '@M' => 
  array (
    0 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
  ),
  '@M ' => 
  array (
    0 => 'M POP 5020A',
    1 => 'M POP',
  ),
  '@M-' => 
  array (
    0 => 'M-270',
    1 => 'M-PAD N8',
  ),
  '@M0' => 
  array (
    0 => 'M01',
    1 => 'M02',
    2 => 'M01T',
    3 => 'M009F',
    4 => 'M030',
    5 => 'M031',
    6 => 'M032',
    7 => 'M040',
    8 => 'M045',
  ),
  '@M1' => 
  array (
    0 => 'M19',
    1 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    2 => 'M1050S',
    3 => 'M1',
    4 => 'm1 metal',
    5 => 'm1 note',
    6 => 'M1001',
    7 => 'M12',
    8 => 'M1908C3JGG',
  ),
  '@M2' => 
  array (
    0 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    1 => 'M210',
    2 => 'm2 note',
    3 => 'm2',
    4 => 'M2mini',
    5 => 'M2002J9G',
    6 => 'M2003J15SC',
    7 => 'M2004J19C',
    8 => 'M2004J7BC',
    9 => 'M2006C3LG',
    10 => 'M2006C3LVG',
    11 => 'M2006C3MG',
    12 => 'M2006C3MII',
    13 => 'M2006C3MNG',
    14 => 'M2007J17G',
    15 => 'M2007J1SC',
    16 => 'M2007J20CG',
    17 => 'M2007J22G',
    18 => 'M2007J3SG',
    19 => 'M2007J3SY',
    20 => 'M2010J19CG',
    21 => 'M2010J19SG',
    22 => 'M2010J19SY',
    23 => 'M2011K2C',
    24 => 'M2011K2G',
    25 => 'M2012K11AG',
    26 => 'M2012K11AC',
    27 => 'M2012K10C',
    28 => 'M2012K11G',
    29 => 'M2101K6G',
    30 => 'M2101K6P',
    31 => 'M2101K7AG',
    32 => 'M2101K7BG',
    33 => 'M2101K7BNY',
    34 => 'M2101K9AG',
    35 => 'M2101K9C',
    36 => 'M2101K9G',
    37 => 'M2102J20SG',
    38 => 'M2102J20SI',
    39 => 'M2102K1AC',
    40 => 'M2102K1C',
    41 => 'M2102K1G',
    42 => 'M2103K19G',
    43 => 'M2103K19PG',
  ),
  '@M3' => 
  array (
    0 => 'M3 Enjoy TV Box',
    1 => 'M3-2200',
    2 => 'M305',
    3 => 'M350',
    4 => 'M363',
    5 => 'M310',
    6 => 'M370i',
    7 => 'm3 note',
    8 => 'm3',
    9 => 'M3s',
    10 => 'M351',
    11 => 'M353',
    12 => 'M355',
    13 => 'M356',
  ),
  '@M4' => 
  array (
    0 => 'M4301',
    1 => 'M470BSA',
    2 => 'M470BSE',
    3 => 'M4',
    4 => 'M4(TEL)? SS[0-9]{3,4}!!',
    5 => 'M40 EEA',
    6 => 'M40(N9H3)',
  ),
  '@M5' => 
  array (
    0 => 'M555',
    1 => 'M532',
    2 => 'M5 lite',
    3 => 'M571C',
    4 => 'M5 Note',
    5 => 'M578C',
    6 => 'M5',
  ),
  '@M6' => 
  array (
    0 => 'M623C',
    1 => 'M650',
    2 => 'M660',
    3 => 'M6 Note',
    4 => 'M6',
  ),
  '@M7' => 
  array (
    0 => 'M701C',
    1 => 'M702',
    2 => 'M758A',
    3 => 'M7CDTU',
    4 => 'M70',
    5 => 'M785',
    6 => 'M726HC',
    7 => 'M70014',
  ),
  '@M8' => 
  array (
    0 => 'M801',
    1 => 'M8047IU',
    2 => 'M812C',
    3 => 'm8wl',
    4 => 'M860',
    5 => 'M865',
    6 => 'M886',
    7 => 'M8',
    8 => 'M8 pro',
  ),
  '@M9' => 
  array (
    0 => 'M975',
    1 => 'M9300',
    2 => 'M9',
    3 => 'M9-unlocked',
    4 => 'M9 pro',
    5 => 'M9000',
  ),
  '@MA' => 
  array (
    0 => 'Maguro',
    1 => 'Mako',
    2 => 'manta',
    3 => '(bq|Aquaris|Edison|Maxwell)!!',
    4 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
    5 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    6 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    7 => '(HW-|HUAWEI )?MAR-(AL00|LX1A|LX1M|LX2|LX3A|TL00)!',
    8 => 'MAX FIND 5.0',
    9 => 'Malata!!',
    10 => 'Masstel!!',
    11 => 'MASTONE!!',
    12 => 'Maxtron!!',
    13 => 'magi',
    14 => 'MagicBox',
    15 => '(Xiaomi )?(MI )?MAX$!',
    16 => '(Xiaomi )?(MI )?MAX 2$!',
    17 => '(Xiaomi )?(MI )?MAX 3$!',
    18 => 'markf',
    19 => 'markw',
  ),
  '@MB' => 
  array (
    0 => 'MBX DVBT reference board (c03ref)',
    1 => 'MBX Dongle board!',
    2 => 'MBX reference board!',
    3 => '(NMP|MBR|XDK|XDS|XMP)\\-!!',
    4 => 'MBS BEAN 454',
    5 => 'MB[0-9]{3,3}!!',
  ),
  '@MC' => 
  array (
    0 => 'MC002',
    1 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
    2 => 'MC401 GWL',
    3 => 'MC32N0',
    4 => 'MC40N0',
    5 => 'MC67NA',
  ),
  '@MD' => 
  array (
    0 => '(MEDION|(MD )?LIFETAB)!!',
    1 => 'MD-[0-9]{4,4}!!',
  ),
  '@ME' => 
  array (
    0 => 'ME102A',
    1 => 'ME180A',
    2 => 'ME171',
    3 => 'ME172V',
    4 => 'ME173X',
    5 => 'ME301T',
    6 => 'ME302C',
    7 => 'ME302KL',
    8 => 'ME370T',
    9 => 'ME371MG',
    10 => 'ME372CG',
    11 => 'ME372CL-FF',
    12 => 'ME560CG',
    13 => 'ME581CL',
    14 => 'MeMO Pad 7',
    15 => 'MediaPad!!',
    16 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    17 => 'Mediacom 810C',
    18 => '(MEDION|(MD )?LIFETAB)!!',
    19 => 'MEEG!!',
    20 => 'MEIZU E3',
    21 => 'meizu m1note',
    22 => 'MEIZU M5',
    23 => 'MEIZU M9',
    24 => 'MEIZU MX',
    25 => 'Meizu S6',
    26 => '(MEO )?Smart A!!',
    27 => 'MEU AN!!',
    28 => 'ME[0-9]{3,3}!!',
    29 => 'MEM02',
    30 => 'MegaFon SP-AI',
    31 => 'MegaFon SP-A3',
    32 => 'MegaFon SP-A5',
    33 => 'MegaFon SP-A10',
    34 => 'MegaFon V9',
    35 => 'MegaFon V9+',
    36 => 'MegaFon SIM+',
    37 => 'MegaFon Login 3',
  ),
  '@MF' => 
  array (
    0 => 'mfld (dv10|dv20|gi|lw00|pr2|pr3)!',
    1 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
    2 => 'MFC[0-9]!!',
    3 => 'MF97B',
  ),
  '@MH' => 
  array (
    0 => 'MH350',
    1 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
  ),
  '@MI' => 
  array (
    0 => 'MID1040C',
    1 => 'MID[0-9]{4,4}!!',
    2 => 'Mini 3iG',
    3 => 'MINT-DG330',
    4 => 'MID1018',
    5 => 'MID1028',
    6 => 'MID Ser(ai|ia)ls!',
    7 => 'MIUI.us Sensation 4G',
    8 => 'MID[0-9]!!',
    9 => 'Mi-A402',
    10 => 'Micromax!!',
    11 => 'Mio!!',
    12 => 'MITO!!',
    13 => 'MIZ Z2',
    14 => 'Milestone XT711',
    15 => 'Milestone XT720',
    16 => 'Milestone',
    17 => 'Milestone X',
    18 => 'Milestone X2',
    19 => 'Milestone MAXX',
    20 => 'MILESTONE3',
    21 => 'MID7C',
    22 => 'MID43C',
    23 => 'MID74C',
    24 => 'MID77C',
    25 => 'MID82C',
    26 => 'MID84C',
    27 => 'MID102C',
    28 => 'MID103C',
    29 => 'MID104C',
    30 => 'MID114C',
    31 => 'MID[C012]!!',
    32 => 'Mi438S',
    33 => '(CSL[- ])?(Spice[- ]?)?Mi(-| )?[0-9]{3,3}!!',
    34 => 'miTab!!',
    35 => '(Xiaomi )?(Xiaomi|Xiaomi M|MI)!!',
    36 => 'MI 8',
    37 => 'MI 8 SE',
    38 => 'MI 8 UD',
    39 => 'MI 8 Lite',
    40 => 'MI 8 Pro',
    41 => 'MI 9',
    42 => 'Mi 9 Lite',
    43 => 'Mi9 Pro 5G',
    44 => 'Mi 9 SE',
    45 => 'Mi 9T',
    46 => 'Mi 9T Pro',
    47 => 'Mi 10',
    48 => 'Mi 10 Pro',
    49 => 'Mi 10 Lite 5G',
    50 => 'Mi 10T Lite',
    51 => 'Mi 11',
    52 => 'Mi 11 Lite 5G',
    53 => 'Mi Note 10',
    54 => 'Mi Note 10 Lite',
    55 => 'Mi Note 10 Pro',
    56 => '(Xiaomi|Xiaomi Mi|MI) Note!!',
    57 => '(Xiaomi )?(MI )?MAX$!',
    58 => '(Xiaomi )?(MI )?MAX 2$!',
    59 => '(Xiaomi )?(MI )?MAX 3$!',
    60 => '(Xiaomi )?(MI )?MIX$!',
    61 => '(Xiaomi )?(MI )?MIX 2$!',
    62 => '(Xiaomi )?(MI )?MIX 2S$!',
    63 => 'MIX 3',
    64 => 'Mi MIX 3',
    65 => 'Mi MIX 3 5G',
    66 => 'MI PLAY',
    67 => 'mido',
    68 => '(Xiaomi|Xiaomi Mi|MI) Pad!!',
    69 => '(Xiaomi|Xiaomi Mi|MI)Box!!',
    70 => '(Xiaomi|Xiaomi Mi|MI)TV!!',
    71 => '(ZTE )?(Grand|Mimosa)!!',
  ),
  '@MK' => 
  array (
    0 => 'MK808',
    1 => 'MK808B',
    2 => 'MK809',
    3 => 'MK809B',
    4 => 'MK809IV',
    5 => 'MK818B',
    6 => 'MK908',
    7 => 'MK908II',
    8 => 'MK16(a|i|$)!',
  ),
  '@ML' => 
  array (
    0 => '(HW-|HUAWEI )?MLA-(L01|L03|L11|L12|L13)!',
    1 => '(HW-|HUAWEI )?MLA-(AL00|AL10)!',
    2 => '(HW-|HUAWEI )?MLA-(TL00|TL10|UL00)!',
    3 => 'MLLED!!',
  ),
  '@MM' => 
  array (
    0 => 'MM-3201',
  ),
  '@MO' => 
  array (
    0 => 'Moto E',
    1 => 'Moto G',
    2 => 'Moto G LTE',
    3 => 'Moto G 2014',
    4 => 'Moto G 2014 LTE',
    5 => 'Moto G (2014)',
    6 => 'Moto X',
    7 => 'Moto 360',
    8 => 'Mobile Sosh',
    9 => 'Monterra',
    10 => 'Mobiistar!!',
    11 => 'MOCHE SMART A16',
    12 => '(MODECOM )?FreeTAB!!',
    13 => 'MOGU!!',
    14 => 'moii!!',
    15 => 'MORAL N01',
    16 => 'motorola one',
    17 => 'motorola one 5G ace',
    18 => 'motorola one action',
    19 => 'motorola one fusion+',
    20 => 'motorola one hyper',
    21 => 'motorola one macro',
    22 => 'motorola one power',
    23 => 'motorola one vision',
    24 => 'motorola one zoom',
    25 => 'motorola edge',
    26 => 'motorola edge plus',
    27 => 'motorola edge 20',
    28 => 'motorola edge 20 lite',
    29 => 'motorola edge 20 pro',
    30 => 'motorola edge 30 pro',
    31 => 'Moto Droid',
    32 => 'Motorola Pro XT610',
    33 => 'Motorola Milestone MAXX',
    34 => 'Motorola Backflip Me600',
    35 => 'Moto ME860',
    36 => 'Moto C',
    37 => 'Moto C Plus',
    38 => '(Moto)?E2$!',
    39 => '(Moto)?E2\\(4G-LTE\\)$!',
    40 => 'Motorola MotoE2!',
    41 => 'Moto E4',
    42 => 'Moto E (4)',
    43 => 'Moto E (4) Plus',
    44 => 'moto e5',
    45 => 'moto e5 cruise',
    46 => 'moto e5 play',
    47 => 'moto e5 plus',
    48 => 'moto e(6i)',
    49 => 'moto e6s',
    50 => 'moto e6 play',
    51 => 'moto e(6) plus',
    52 => 'moto e(7)',
    53 => 'moto e(7) plus',
    54 => 'moto e(7) power',
    55 => 'moto e(7i) power',
    56 => 'moto e20',
    57 => 'moto e30',
    58 => 'moto e40',
    59 => '(Moto)?G3$!',
    60 => '(Moto)?G3-TE$!',
    61 => 'Moto G 2015',
    62 => 'Moto G (2015)',
    63 => 'Moto G (2014',
    64 => 'MOTOROLA MOTOG',
    65 => 'Moto G Play',
    66 => 'moto g power',
    67 => 'Moto G Turbo',
    68 => 'moto g 5G',
    69 => 'moto g 5G plus',
    70 => 'moto g pro',
    71 => 'Moto G4',
    72 => 'Moto G (4',
    73 => 'Moto G (4)',
    74 => 'Moto G4 Plus',
    75 => 'Moto G 5',
    76 => 'Moto G (5',
    77 => 'Moto G (5)',
    78 => 'Moto G (5) Plus',
    79 => 'Moto G (5S',
    80 => 'Moto G (5S)',
    81 => 'Moto G (5S) Plus',
    82 => 'Moto G (5S) Plus (XT1805)',
    83 => 'moto g(6',
    84 => 'moto g(6)',
    85 => 'moto g(6) (XT1925DL)',
    86 => 'moto g(6) forge',
    87 => 'moto g(6) play',
    88 => 'moto g(6) plus',
    89 => 'moto g(7',
    90 => 'moto g(7)',
    91 => 'moto g(7) optimo (XT1952DL)',
    92 => 'moto g(7) play',
    93 => 'moto g(7) plus',
    94 => 'moto g(7) power',
    95 => 'moto g(7) supra',
    96 => 'moto g(8',
    97 => 'moto g(8)',
    98 => 'moto g(8) plus',
    99 => 'moto g(8) power',
    100 => 'moto g(8) power lite',
    101 => 'moto g(9) play',
    102 => 'moto g(9) plus',
    103 => 'moto g(9) power',
    104 => 'moto g(10)',
    105 => 'moto g(20)',
    106 => 'moto g(30)',
    107 => 'moto g31(w)',
    108 => 'moto g(50)',
    109 => 'moto g51 5G',
    110 => 'moto g(60)',
    111 => 'moto g(60)s',
    112 => 'moto g(100)',
    113 => 'moto g200 5G',
    114 => 'Moto Z (2)',
    115 => 'Moto Z2 Play',
    116 => 'Moto Z3 Play',
    117 => 'Moto X Pro',
    118 => 'moto x4',
    119 => 'moto z4',
    120 => 'Moto Backflip',
    121 => 'motorola razr 5G',
    122 => 'MOTOROLA XOOM MZ606',
    123 => 'MOTOROLA RAZR',
    124 => 'MOTWX435KT',
    125 => '(PLOYER-)?MOMO!!',
    126 => 'MOB-5045',
    127 => 'Mobii 7',
    128 => 'MoFing',
    129 => 'mocha',
    130 => 'MO-01J',
    131 => 'MO-01K',
    132 => 'Movistar Express',
    133 => 'Movistar Link',
    134 => 'Movistar Motion',
    135 => 'Movistar Prime',
    136 => 'MOVE',
  ),
  '@MP' => 
  array (
    0 => 'MP[0-9]{3,3}C!!',
    1 => 'MP717',
    2 => 'MP843',
    3 => 'MP888',
    4 => 'MP959',
    5 => 'MP969',
    6 => 'MP1010',
    7 => 'MP7007',
    8 => 'MPDC100 BT',
    9 => 'MPDC110 BT IPS',
    10 => 'MPDC112 BT IPS',
    11 => 'MPDC8 BT',
    12 => 'MPDC88 BT IPS',
    13 => 'MPDC706',
    14 => 'MPDC903',
    15 => 'MPQC704 HD',
    16 => 'MPQC784 IPS',
    17 => 'MPQC804HD',
    18 => 'MPQC1010',
  ),
  '@MR' => 
  array (
    0 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
  ),
  '@MS' => 
  array (
    0 => 'MStar Amber3',
    1 => 'MSM',
    2 => 'msm(7227|7627)!',
    3 => 'msm7630!',
    4 => 'msm8660!',
    5 => 'msm(8x25|8625|8960)!',
    6 => 'msm8x30!',
    7 => 'msm8610!',
    8 => 'msm8909!',
    9 => 'msm(8226|8228)!',
    10 => 'MSM8926!',
    11 => 'msm8916!',
    12 => 'msm8952!',
    13 => 'msm8974!',
    14 => 'MSM8992!',
    15 => 'msm8994!',
    16 => 'MS840 4G',
    17 => 'MS3B',
    18 => 'MS4A',
    19 => 'MS3A',
  ),
  '@MT' => 
  array (
    0 => 'mt5396!',
    1 => 'mt5399!',
    2 => 'mt5861!',
    3 => 'mt5880!',
    4 => 'mt5882!',
    5 => 'mt5890!',
    6 => 'mt6515!',
    7 => 'MTK6515',
    8 => 'mt6517!',
    9 => 'mt6571!',
    10 => 'mt6572!',
    11 => 'mt6575!',
    12 => 'mt6577!',
    13 => 'mt6582!',
    14 => 'mt6589!',
    15 => 'MTK-MT6589',
    16 => 'MTK6589!',
    17 => 'mt6592!',
    18 => 'MTK-MT6592',
    19 => 'MTK6592',
    20 => 'mt6595!',
    21 => 'mt6599!',
    22 => 'mt8125!',
    23 => 'mt8127!',
    24 => 'mt8135!',
    25 => 'MTK--8312',
    26 => 'mt8377!',
    27 => 'mt8389!',
    28 => 'mt8580!',
    29 => 'mt8658!',
    30 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
    31 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    32 => 'MTC Android',
    33 => 'MT791',
    34 => 'MT70[0-9]{2,2}!!',
    35 => 'MTK6516',
    36 => 'MT[0-9]{3,3}!!',
    37 => 'MTS-SP100',
    38 => 'MTS-SP101',
    39 => 'MT[0-9]{2,2}[a-z]!!',
    40 => 'MT7A',
    41 => 'MTC!!',
  ),
  '@MU' => 
  array (
    0 => 'muPAD 7M',
    1 => 'Multilaser Diamond',
    2 => 'Multilaser Orion',
    3 => 'Multilaser Oxy',
    4 => 'Multilaser Titanium',
  ),
  '@MW' => 
  array (
    0 => 'MWND1',
    1 => 'mw07',
    2 => 'MW0710',
    3 => 'MW0711',
    4 => 'MW0712',
    5 => 'MW0731',
    6 => 'MW0731PLUS',
    7 => 'MW0733',
    8 => 'MW0811',
    9 => 'MW0812',
    10 => 'MW0812 V2.0',
    11 => 'MW0821',
    12 => 'MW0821 V2.0',
    13 => 'MW0821+',
    14 => 'MW0831',
    15 => 'MW0831Plus',
    16 => 'MW0922',
    17 => 'MW0931',
    18 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
    19 => 'MW1031',
  ),
  '@MX' => 
  array (
    0 => 'MX Enjoy TV BOX',
    1 => 'MX2',
    2 => 'MX4',
    3 => 'MX4 Pro',
    4 => 'MX5',
    5 => 'MX6',
  ),
  '@MY' => 
  array (
    0 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    1 => 'MyPhone A818g Duo',
    2 => 'MyPhone A818 Slim Duo',
    3 => 'MyPhone A848 Duo',
    4 => 'MyPhone A848i Duo',
    5 => 'MyPhone A848g Duo',
    6 => 'MyPhone A888',
    7 => 'MyPhone A888 Duo',
    8 => 'My|Phone A888 Duo',
    9 => 'MyPhone A919 Duo',
    10 => 'MyPhone A919 3D Duo',
    11 => 'MyPhone A919i',
    12 => 'MyPhone A919i Duo',
    13 => 'MyPhone Agua Cyclone',
    14 => 'MyPhone Agua Hail',
    15 => 'MyPhone Agua Iceberg',
    16 => 'MyPhone Agua Iceberg Mini',
    17 => 'MyPhone Agua Iceberg Slim',
    18 => 'MyPhone Agua Infinity',
    19 => 'MyPhone Agua Ocean Lite',
    20 => 'MyPhone Agua Ocean Mini',
    21 => 'MyPhone Agua Ocean Pro',
    22 => 'MyPhone Agua Rain 2G',
    23 => 'MyPhone Agua Rain 3G',
    24 => 'MyPhone Agua Rio',
    25 => 'MyPhone Agua Rio Fun',
    26 => 'MyPhone Agua Rio Lite',
    27 => 'MyPhone Agua Storm',
    28 => 'MyPhone Agua Vortex',
    29 => 'myPhone C Smart',
    30 => 'myPhone Cube',
    31 => 'myPhone Cube 16GB',
    32 => 'myPhone Duosmart',
    33 => 'myPhone FUN 2',
    34 => 'myPhone Funky',
    35 => 'myPhone Hammer',
    36 => 'myPhone INFINITY 3G',
    37 => 'myPhone NEXT',
    38 => 'myPhone NEXTS',
    39 => 'myPhone NEXT S',
    40 => 'myPhone Smart',
    41 => 'myPhone S-line',
    42 => 'MyPhone Rio Craze',
    43 => 'MyPhone Rio Grande',
    44 => 'MyPhone Rio Junior TV',
    45 => 'MyPhone Rio 2',
    46 => 'MyPhone Rio 2 Fun',
    47 => 'MyPhone UNO',
    48 => 'MyPhone Xperia Rain 3G',
    49 => 'Myphone MY25',
    50 => 'MyPhone MY26',
    51 => 'MyPhone MY28S',
    52 => 'MyPad 2',
    53 => 'MyPad 750HD',
    54 => 'MyPad 1000 HD',
    55 => 'MY ?SAGA!!',
    56 => 'My ?Touch ?4G$!',
    57 => 'myTouch 4G Slide',
    58 => 'myTouch Slide 4G',
  ),
  '@MZ' => 
  array (
    0 => 'MZ-m2 note',
    1 => 'MZ-M571C',
    2 => 'MZ-m3 note',
    3 => 'MZ-M5',
    4 => 'MZ-MEIZU M6',
    5 => 'MZ-MX4',
    6 => 'MZ-MX4 Pro',
    7 => 'MZ-MX5',
    8 => 'MZ[0-9]{3,3}!!',
  ),
  '@N-' => 
  array (
    0 => 'N-0[0-9][A-Z]!!',
  ),
  '@N1' => 
  array (
    0 => 'N1',
    1 => 'N12',
    2 => 'N12R',
    3 => 'N101 DUAL CORE!',
  ),
  '@N2' => 
  array (
    0 => 'N2T',
  ),
  '@N3' => 
  array (
    0 => 'N3-2200',
    1 => 'N320',
  ),
  '@N5' => 
  array (
    0 => 'N51(10|11|16|17)!',
    1 => 'N52(06|07|09)!',
    2 => 'N5PRO2jingying',
    3 => 'N50',
    4 => 'N50DT!',
    5 => 'N50GT',
    6 => 'N50GT A',
  ),
  '@N6' => 
  array (
    0 => 'N612',
    1 => 'N6',
  ),
  '@N7' => 
  array (
    0 => 'N700',
    1 => 'N710',
    2 => 'N70',
    3 => 'N70 3G',
    4 => 'N70 C',
    5 => 'N70DC',
    6 => 'N70-S',
    7 => 'N70HD',
    8 => 'N70 DUAL CORE',
    9 => 'N70DC-S',
    10 => 'N70DC-T',
  ),
  '@N8' => 
  array (
    0 => 'N8730-411',
    1 => 'N8730-41101',
    2 => 'N8730-41102',
    3 => 'N80($| from moage.com)!',
    4 => 'N80DC',
    5 => 'N80IPS',
    6 => 'N861',
  ),
  '@N9' => 
  array (
    0 => 'N930',
    1 => 'N9600',
    2 => 'N90',
    3 => 'N90 DUAL CORE!',
    4 => 'N90FHDRK',
    5 => 'N918St',
  ),
  '@NA' => 
  array (
    0 => 'NABI!!',
    1 => 'NaviPad TM-7055HD 3G',
    2 => 'NaviPad TM-7855 3G',
    3 => 'NaviPad TM-7858 3G (revision 1)',
    4 => 'natrium',
    5 => 'NATCOM N8302',
  ),
  '@NB' => 
  array (
    0 => 'NB036',
    1 => 'NBX-T7013N',
    2 => 'NBX-T7023N',
  ),
  '@NC' => 
  array (
    0 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
  ),
  '@NE' => 
  array (
    0 => '(HTC )?Nexus ?One!',
    1 => '(Google )?Nexus S!',
    2 => '(Google )?Nexus ?4!',
    3 => 'Nexus 5X',
    4 => '(Google )?Nexus ?5!',
    5 => 'Nexus 6P',
    6 => '(Google )?Nexus ?6P!',
    7 => '(Google )?Nexus ?6!',
    8 => '(Google )?Nexus ?7 2013!',
    9 => '(Google )?Nexus ?7!',
    10 => '(Google )?Nexus ?9!',
    11 => '(Google )?Nexus ?10!',
    12 => 'Nexus Player',
    13 => 'NEO-G4',
    14 => 'NEO-G4-108A',
    15 => 'NEO-G4 a',
    16 => 'NEO-U9-H',
    17 => 'NEO-X5',
    18 => 'NEO X5',
    19 => 'Neo-X5-B',
    20 => 'NEO-X5-116A',
    21 => 'NEO-X5-mini',
    22 => 'NEO X5 mini',
    23 => 'NEO-X6',
    24 => 'NEO-X7-216A',
    25 => 'NEO-X7-mini',
    26 => 'NEO-X8',
    27 => 'NEO-X8-H',
    28 => 'NEO-X8H-PLUS',
    29 => 'Neon',
    30 => '(BLU|DASH|LIFE|NEO|STUDIO|VIVO)!!',
    31 => 'NexusHD2',
    32 => 'Nexus HD2',
    33 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    34 => 'Neon7',
    35 => 'Neon9',
    36 => '(NetTab|NT-)!!',
    37 => 'Newman K1',
    38 => 'NewmanK1',
    39 => 'Newman K2',
    40 => 'Newman N1',
    41 => 'Newman N2!',
    42 => 'Newman-N2!',
    43 => 'Newpad',
    44 => 'Newpad-K97',
    45 => 'Newpad-NP900',
    46 => 'Newpad P9',
    47 => 'Newpad P72',
    48 => 'Newsmy P72',
    49 => 'Newsmy P72-C',
    50 => 'Newsmy T3',
    51 => 'Newsmy T7II',
    52 => 'NEC-STR',
    53 => 'NEC-0912',
    54 => 'NEC-101S',
    55 => 'NECTEL-101S',
    56 => 'NEC-101T',
    57 => 'NEC-101TH',
    58 => 'NEC-102',
    59 => 'NE-103T',
    60 => 'NEC909e',
    61 => 'NE-202',
    62 => 'NE-201A1A',
    63 => 'NEC-NE-201A1A',
    64 => 'NEC-NEC-NE-201A1A',
    65 => 'NEC PC-TE508S1',
    66 => 'neken!!',
    67 => 'NEO!!',
    68 => '(Nexian )?NX-A[0-9]{3,3}!!',
    69 => 'Nextbook Premium 7',
    70 => 'Next7C12!',
    71 => 'Next7D12!',
    72 => 'Next7P12!',
    73 => 'Next7Q12!',
    74 => 'Next7P',
    75 => 'Next7S',
    76 => 'Nextbook Premium 7 SE',
    77 => 'NEXT8P',
    78 => 'Next8P11',
    79 => 'Next8P12',
    80 => 'Next9P',
    81 => 'Next10P12',
    82 => 'NEXT',
    83 => 'NE2213',
    84 => 'New Andromax-i',
    85 => 'Neo V',
    86 => 'NEBULA6.9',
  ),
  '@NG' => 
  array (
    0 => 'NGM Black Hole',
    1 => 'NGM Dynamic Racing 2',
    2 => 'NGM Dynamic Racing 3',
    3 => 'NGM Dynamic Star',
    4 => 'NGM Dynamic Stylo',
    5 => 'NGM Forward Infinity',
    6 => 'NGM Forward Racing HD',
    7 => 'NGM Forward Young',
    8 => 'NGM Harley',
    9 => 'NGM Orion',
    10 => 'NGM Spirit',
    11 => 'NGM Time',
    12 => 'NGM Vanity Smart',
    13 => 'NGM Action',
    14 => 'NGM Legend',
    15 => 'NGM-Legend/Legend',
    16 => 'NGM Legend2',
    17 => 'NGM LegendXL',
    18 => 'NGM Miracle',
    19 => 'NGM P0laris',
    20 => 'NGM Wilco',
    21 => 'NGM WINN',
  ),
  '@NI' => 
  array (
    0 => 'NITROGEN6X',
    1 => 'NID 7010',
    2 => 'Nibiru H1',
    3 => 'Nibiru H1c',
    4 => 'nine i7400',
    5 => 'nikeh',
    6 => 'nikel',
  ),
  '@NM' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    1 => '(NMP|MBR|XDK|XDS|XMP)\\-!!',
  ),
  '@NO' => 
  array (
    0 => 'Novo7',
    1 => 'Novo7 -?Aurora!',
    2 => 'novo Aurora',
    3 => 'Novo 7 Aurora',
    4 => 'Novo7 Advanced',
    5 => 'Novo7Advanced!',
    6 => 'Novo7 Advanced2',
    7 => 'Novo7 Basic',
    8 => 'NovoCrystal',
    9 => 'Novo 7 Crystal!',
    10 => 'Novo7 ELF',
    11 => 'Novo7 Fire',
    12 => 'Novo7 Flame',
    13 => 'Novo7 Grace',
    14 => 'Novo7 Legend',
    15 => 'novo7Legend!',
    16 => 'NOVO7 Mars',
    17 => 'Novo7 PALADIN',
    18 => 'Novo7 Rainbow',
    19 => 'Novo7 -Rainbow',
    20 => 'NOVO7 Tornados',
    21 => 'Novo7 Venus',
    22 => 'Novo 7 Venus',
    23 => 'Novo7 Venus QuadCore',
    24 => 'Novo 7 Venus QuadCore',
    25 => 'Novo8 Advanced',
    26 => 'Novo8 Discover Quadcore',
    27 => 'Novo8 Disover Quadcore',
    28 => 'Novo8 Dream QuadCore',
    29 => 'Novo8mini',
    30 => 'novo9-Spark',
    31 => 'NOVO9 Spark II',
    32 => 'Novo 10 Hero',
    33 => 'Novo10 ?Hero!',
    34 => 'Novo 10 Hero QuadCore',
    35 => 'Novo Captain',
    36 => 'Novo10 captain QuadCore',
    37 => 'NOOK',
    38 => 'Nook ?Color!',
    39 => '(NOOK )?BNRV(200|300)!',
    40 => 'Nook ?Tablet!',
    41 => 'NOOK Slate',
    42 => '(NOOK )?BNTV250!',
    43 => '(NOOK )?BNRV350!',
    44 => '(NOOK )?BNTV(400)!',
    45 => '(NOOK )?BNTV(450)!',
    46 => '(NOOK )?BNTV(600)!',
    47 => '(NOOK )?BNTV(800)!',
    48 => 'Nook HD',
    49 => 'Nook HD+',
    50 => 'Novapad!!',
    51 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    52 => 'Nova!!',
    53 => 'Now',
    54 => 'NO1 S6',
    55 => 'NOAIN!!',
    56 => 'Nokia 1 Plus',
    57 => 'Nokia 1',
    58 => 'Nokia 2.1',
    59 => 'Nokia 2',
    60 => 'Nokia 3.1 Plus',
    61 => 'Nokia 3.1',
    62 => 'Nokia 4.2',
    63 => 'Nokia 5.1 Plus',
    64 => 'Nokia 5.1',
    65 => 'Nokia 6.1 Plus',
    66 => 'Nokia 6.1',
    67 => 'Nokia 6.2',
    68 => 'Nokia 6',
    69 => 'Nokia 7.1',
    70 => 'Nokia 7 Plus',
    71 => 'Nokia 8.1',
    72 => 'Nokia 8 Sirocco',
    73 => 'Nokia 8',
    74 => 'Nokia 9',
    75 => 'Nokia 10.1',
    76 => 'Nokia N9',
    77 => 'Nokia N900',
    78 => 'Nokia X',
    79 => 'Nokia X Dual SIM',
    80 => 'Nokia X+',
    81 => 'Nokia Xplus',
    82 => 'Nokia XL 4G',
    83 => 'Nokia XL',
    84 => 'Nokia XL Dual SIM',
    85 => 'Nokia X2',
    86 => 'NokiaX2DS',
    87 => 'Notion Ink ADAM',
    88 => 'Note III',
    89 => 'Nozomi',
    90 => 'Note 7P',
    91 => 'Note 8P',
    92 => 'Note 9P',
    93 => 'Note 11P',
    94 => 'Noble TAB07-485',
    95 => '(QMobile|QTab|Q-Smart|Noir)!!',
  ),
  '@NP' => 
  array (
    0 => 'npm702-NOVO7PALADIN',
  ),
  '@NS' => 
  array (
    0 => 'NS115',
    1 => 'NS2816',
    2 => 'NS-14T002',
    3 => 'NS-13T001',
    4 => 'NSZ-G[A-Z][0-9]!!',
  ),
  '@NT' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    1 => '(NetTab|NT-)!!',
  ),
  '@NU' => 
  array (
    0 => 'Numy!!',
    1 => 'nuvi!!',
    2 => 'NUU A3',
    3 => 'NuclearSX-SP5',
    4 => 'nubia Z5',
    5 => 'Nubia Z9 Max',
    6 => 'Nura 2',
  ),
  '@NW' => 
  array (
    0 => 'NWZ?-Z1000Series!',
  ),
  '@NX' => 
  array (
    0 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    1 => '(Nexian )?NX-A[0-9]{3,3}!!',
    2 => 'NXA8QC116',
    3 => 'NX16A8116K',
    4 => 'NXM726',
    5 => 'NXM726HN',
    6 => 'NXM726HN C030',
    7 => 'NXM726HN HASTINGS',
    8 => 'NXM727KC!',
    9 => 'NXM803HC',
    10 => 'NXM803HD',
    11 => 'NX868QW8G',
    12 => 'NXM805ND',
    13 => 'NXM805ND EN',
    14 => 'NXM908HC',
    15 => 'NXM703U',
    16 => 'NXM901',
    17 => 'NXM736',
    18 => 'NX700QC',
    19 => 'NX785QC8G',
    20 => 'NXM900MC',
    21 => '(ZXY-)?NX[0-9]{2,3}!!',
  ),
  '@NY' => 
  array (
    0 => 'NYX!!',
  ),
  '@O2' => 
  array (
    0 => 'O2 Joggler',
  ),
  '@OB' => 
  array (
    0 => 'OB-OPPO A31c',
    1 => 'OB-OPPO R8205',
    2 => 'OB-OPPO R7005',
    3 => 'OB-OPPO R7c',
    4 => 'OB-OPPO 1105',
    5 => 'OB-OPPO 3005',
    6 => 'Obreey SURFpad',
  ),
  '@OC' => 
  array (
    0 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
  ),
  '@OD' => 
  array (
    0 => 'odroida',
    1 => 'ODROID-A',
    2 => 'ODROIDC',
    3 => 'ODROIDC2',
    4 => 'ODROID-U',
    5 => 'ODROID-U2',
    6 => 'ODROID-X',
    7 => 'ODROID-XU',
    8 => 'ODROID-XU3',
    9 => 'ODROID-XU3-ATV',
    10 => 'ODROID-XU4',
    11 => 'ODROID-X2',
    12 => 'ODYS-Chrono',
    13 => 'ODYS-EVO',
    14 => 'Odys-Loox',
    15 => 'ODYS-NOON',
    16 => 'ODYS-Q',
    17 => 'ODYS Space',
    18 => 'ODYS Space TKI BRR233v7',
    19 => 'ODYS Space TKI BRR233v9',
    20 => 'ODYS-Xpress',
    21 => 'ODYS Xtreme',
    22 => 'OD103',
  ),
  '@OL' => 
  array (
    0 => 'Olivetti Olipad 100',
  ),
  '@OM' => 
  array (
    0 => 'Omap5panda',
    1 => 'OMAP',
    2 => 'OMAP SS',
    3 => 'omap3evm',
    4 => 'OMAP3ETPP',
    5 => 'Omap5sevm',
    6 => 'OMS TTD',
    7 => 'OMS1 6',
    8 => 'omega',
    9 => 'Omega 5.0',
    10 => 'Omega 5.5',
  ),
  '@ON' => 
  array (
    0 => 'One 7 M77GHG',
    1 => 'OnePAD 785I',
    2 => 'OnePAD 900HD',
    3 => 'OnePAD 970',
    4 => 'OnePAD 1100x2',
    5 => 'ONETOUCH Flash Plus',
    6 => 'one touch 890!',
    7 => 'one touch 891!',
    8 => 'one touch 900!',
    9 => 'one touch 903!',
    10 => 'one touch 906!',
    11 => 'one touch 908!',
    12 => 'one touch 909!',
    13 => 'one touch 910!',
    14 => 'one touch 913!',
    15 => 'one touch 916!',
    16 => 'one touch 918!',
    17 => 'one touch 922!',
    18 => 'one touch 927!',
    19 => 'one touch 928!',
    20 => 'one touch 930!',
    21 => 'one touch 960!',
    22 => 'one touch 976!',
    23 => 'one touch 978!',
    24 => 'one touch 979 HelloKitty',
    25 => 'one touch 979!',
    26 => 'one touch 980!',
    27 => 'one touch 981!',
    28 => 'one touch 983!',
    29 => 'one touch 985!',
    30 => 'one touch 986!',
    31 => 'one touch 988!',
    32 => 'one touch 990!',
    33 => 'one touch 991!',
    34 => 'one touch 992!',
    35 => 'ONE TOCH 992D',
    36 => 'one touch 993!',
    37 => 'one touch 995!',
    38 => 'one touch 997!',
    39 => 'one touch 998!',
    40 => 'one touch D662',
    41 => 'one touch D668',
    42 => 'one touch D820',
    43 => 'one touch D920',
    44 => 'one touch J320',
    45 => 'one ?touch 40(05|10|11)!',
    46 => 'one ?touch 4007!',
    47 => 'one ?touch 4008!',
    48 => 'one ?touch 4009!',
    49 => 'one ?touch 4013!',
    50 => 'one ?touch 4014!',
    51 => 'one ?touch 40(15|16)!',
    52 => 'one ?touch 4024!',
    53 => 'one ?touch 4027!',
    54 => 'one ?touch 4029!',
    55 => 'one ?touch 4030!',
    56 => 'one ?touch 4031!',
    57 => 'one ?touch 4032!',
    58 => 'one ?touch 4033!',
    59 => 'ONETOUCH POPC3',
    60 => 'one ?touch 40(35|36|37)!',
    61 => 'one ?touch 5045!',
    62 => 'one ?touch 5020!',
    63 => 'one ?touch 5021!',
    64 => 'one ?touch 5035!',
    65 => 'one ?touch 50(36|37)!',
    66 => 'ONETOUCH POPC5',
    67 => 'one ?touch 50(38)!',
    68 => 'one ?touch 5042!',
    69 => 'one ?touch 5050!',
    70 => 'one ?touch 6010!',
    71 => 'one ?touch 6012!',
    72 => 'ONE TOUCH IDOL MINI',
    73 => 'one ?touch 6014!',
    74 => 'one ?touch 6016!',
    75 => 'one ?touch 6030!',
    76 => 'one ?touch 6032!',
    77 => 'one ?touch 6033!',
    78 => 'one ?touch 60(34|35)!',
    79 => 'one ?touch 6036!',
    80 => 'one ?touch 6037!',
    81 => 'one ?touch 6039!',
    82 => 'one ?touch 6040!',
    83 => 'one ?touch 6043!',
    84 => 'one ?touch 6045!',
    85 => 'one ?touch 6050!',
    86 => 'one ?touch 7024!',
    87 => 'one touch fierce!',
    88 => 'one ?touch 70(25|30)!',
    89 => 'one ?touch 70(40|41)!',
    90 => 'one ?touch 7042!',
    91 => 'one ?touch 7043!',
    92 => 'one ?touch 7044!',
    93 => 'one ?touch 7045!',
    94 => 'one ?touch 7047!',
    95 => 'one ?touch 7050!',
    96 => 'one ?touch 8000!',
    97 => 'one ?touch 8008!',
    98 => 'one ?touch 8020!',
    99 => 'one ?touch 8030!',
    100 => 'one ?touch T10!',
    101 => 'one ?touch T20!',
    102 => 'one ?touch T60!',
    103 => 'ONE TOUCH TAB 7',
    104 => 'ONE TOUCH TAB 7HD',
    105 => 'ONE TOUCH TAB 8HD',
    106 => 'ONE TOUCH EVO7',
    107 => 'onetouch EVO7',
    108 => 'ONE TOUCH E710',
    109 => 'ONE TOUCH EVO 7HD',
    110 => 'ONE TOUCH EVO7HD',
    111 => 'ONE TOUCH EVO8HD',
    112 => 'ONETOUCH POP 7 LTE',
    113 => 'ONE ?TOUCH P310(A|X)!',
    114 => 'ONE ?TOUCH P320(A|X)!',
    115 => 'ONE ?TOUCH P321!',
    116 => 'ONE ?TOUCH POP 8S P350X!',
    117 => 'One S',
    118 => 'One X',
    119 => 'One X+',
    120 => 'One XL',
    121 => 'One V',
    122 => 'One',
    123 => 'One M7',
    124 => 'One Max',
    125 => 'one m8',
    126 => 'One A0001',
    127 => 'OnePlus One',
    128 => 'OnePlus One A0001',
    129 => 'ONE A2001',
    130 => 'ONE A2003',
    131 => 'ONE A2005',
    132 => 'OnePlus2',
    133 => 'ONE A3000',
    134 => 'ONE A3003',
    135 => 'ONEPLUS A3000',
    136 => 'ONEPLUS A3003',
    137 => 'OnePlus3',
    138 => 'ONEPLUS A3010',
    139 => 'ONEPLUS A5000',
    140 => 'ONEPLUS A5010',
    141 => 'ONEPLUS A6000',
    142 => 'ONEPLUS A6003',
    143 => 'ONEPLUS A6010',
    144 => 'ONEPLUS A6013',
    145 => 'OnePlus 7T Pro',
    146 => 'ONE E1000',
    147 => 'ONE E1001',
    148 => 'ONE E1003',
    149 => 'ONE E1005',
    150 => 'oneplus Nord 2 5G',
    151 => 'ONDA MID',
    152 => 'ONDA A9 Core4',
    153 => 'ONDA VI10',
    154 => 'Onda V812',
    155 => 'ONDA v975m',
    156 => 'ONDA V989',
    157 => 'ONDA V989 Core8',
  ),
  '@OP' => 
  array (
    0 => 'OPPOLenovo A60',
    1 => 'Optimus!!',
    2 => 'Opus One',
    3 => 'OPS-DRD Digital Signage Player',
    4 => 'OP110',
    5 => 'OPPO PBAM00',
    6 => 'OPPO PBFT00',
    7 => 'OPPO A30',
    8 => 'OPPO A31',
    9 => 'OPPO A31t',
    10 => 'OPPO A33',
    11 => 'OPPO A33m',
    12 => 'OPPO A37m',
    13 => 'OPPO A53',
    14 => 'OPPO A53m',
    15 => 'OPPO A53t',
    16 => 'OPPO A57',
    17 => 'OPPO A57t',
    18 => 'OPPO A59m',
    19 => 'OPPO A59s',
    20 => 'OPPO A59st',
    21 => 'OPPO A73',
    22 => 'OPPO A73t',
    23 => 'OPPO A77',
    24 => 'OPPO A77t',
    25 => 'OPPO A79',
    26 => 'OPPO A79kt',
    27 => 'OPPO A79t',
    28 => 'OPPO A83',
    29 => 'OPPO A83t',
    30 => 'OPPO A800',
    31 => 'OPPOX905',
    32 => 'OPPOX907',
    33 => 'OPPO Find5',
    34 => 'OPPOX909',
    35 => 'OPPO X909',
    36 => 'OPPO X9007',
    37 => 'OPPOX9015',
    38 => 'OPPOX9017',
    39 => 'OPPO find7',
    40 => 'OPPOR601',
    41 => 'OPPOR801',
    42 => 'OPPOR803',
    43 => 'OPPOR805',
    44 => 'OPPOR807',
    45 => 'OPPO R811',
    46 => 'OPPO R813T',
    47 => 'OPPO R815',
    48 => 'OPPOR817',
    49 => 'Oppo R819',
    50 => 'OPPO R821',
    51 => 'OPPO R831',
    52 => 'Oppo R831 Limited Edition',
    53 => 'OPPO R1001',
    54 => 'OPPOR8015',
    55 => 'OPPOR8111',
    56 => 'OPPOT29',
    57 => '(OPPO-?)?T703!',
    58 => 'OPPOU701',
    59 => 'OPPOU7011',
    60 => 'OPPOU7015',
    61 => 'OPPO PBCM30',
    62 => 'OPPO PBCT10',
    63 => 'OPPO N1',
    64 => 'OPPO R7',
    65 => 'OPPO R7Plus',
    66 => 'OPPO R7s',
    67 => 'OPPO R7sm',
    68 => 'OPPO R7st',
    69 => 'OPPO R7t',
    70 => 'OPPO R9k',
    71 => 'OPPO R9m',
    72 => 'OPPO R9tm',
    73 => 'OPPO R9km',
    74 => 'OPPO R9s',
    75 => 'OPPO R9sk',
    76 => 'OPPO R9st',
    77 => 'OPPO R9skt',
    78 => 'OPPO R9t',
    79 => 'OPPO R11',
    80 => 'OPPO R11t',
    81 => 'OPPO R7 Plus',
    82 => 'OPPO R7sPlus',
    83 => 'OPPO R9 Plusm A',
    84 => 'OPPO R9 Plustm A',
    85 => 'OPPO R9s Plus',
    86 => 'OPPO R9s Plust',
    87 => 'OPPO R11 Plus',
    88 => 'OPPO R11 Plusk',
    89 => 'OPPO R11 Pluskt',
    90 => 'OPPO R11s',
    91 => 'OPPO R11s Plus',
    92 => 'OPPO R11st',
    93 => 'OPPO R11s Plust',
    94 => 'OPPO PAAM00',
    95 => 'OPPO PACM00',
    96 => 'OPPO PACT00',
    97 => 'OPPO PBDM00',
    98 => 'OPPO PAFM00',
    99 => 'OPSSON!!',
    100 => 'OP0118-12',
    101 => 'Optimus Barcelona',
    102 => 'Optimus Boston',
    103 => 'Optimus Madrid',
    104 => 'Optimus Monte Carlo',
    105 => 'Optimus San Francisco',
    106 => 'Optimus San Remo',
    107 => 'Optimus Zali',
  ),
  '@OR' => 
  array (
    0 => 'Orange HUAWEI GRA-L09',
    1 => 'Orbis',
    2 => 'Orange Boston',
    3 => 'Orange Covo',
    4 => 'Orange Daytona',
    5 => 'Orange Dublin',
    6 => 'Orange Fova',
    7 => 'Orange Gova',
    8 => 'Orange Hi 4G',
    9 => 'Orange Hiro',
    10 => 'Orange infinity 996',
    11 => 'Orange Infinity 8008X',
    12 => 'Orange Kivo',
    13 => 'Orange Monte Carlo',
    14 => 'Orange Neva 80',
    15 => 'Orange Niva',
    16 => 'Orange Nura',
    17 => 'Orange Novi',
    18 => 'Orange Reyo',
    19 => 'Orange Rise 30',
    20 => 'Orange Rono',
    21 => 'Orange Roya',
    22 => 'Orange Runo',
    23 => 'Orange San Francisco',
    24 => 'Orange Sego',
    25 => 'Orange Tactile internet 2',
    26 => 'Orange Tado',
    27 => 'Orange Yomi',
    28 => 'Orange Yumo',
    29 => 'Orange Zali',
  ),
  '@OT' => 
  array (
    0 => 'OT 918',
    1 => 'OT 919',
    2 => 'OT 919 HelloKitty',
    3 => 'OT-990C',
    4 => 'OT 990M',
    5 => 'OT-995',
  ),
  '@OU' => 
  array (
    0 => 'OUYA( Console)?!',
  ),
  '@OV' => 
  array (
    0 => '(OV-|Overmax|Vertis)!!',
    1 => 'OV10274G',
  ),
  '@OX' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@OY' => 
  array (
    0 => 'Oysters Pacific 800',
    1 => 'Oysters T3 3G',
    2 => 'Oysters T7B 3G',
    3 => 'Oysters T7X 3G',
    4 => 'OYSTERS T14N 3G',
    5 => 'Oysters T37',
    6 => 'Oysters T72H 3G',
    7 => 'Oysters T72HRi 3G',
    8 => 'OYSTERS T80 3G',
  ),
  '@OZ' => 
  array (
    0 => 'OZZY',
  ),
  '@P' => 
  array (
    0 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
  ),
  '@P-' => 
  array (
    0 => 'P-0[0-9][A-Z]!!',
  ),
  '@P0' => 
  array (
    0 => 'P001',
    1 => 'P001 2',
    2 => 'P002',
    3 => 'P00A',
    4 => 'P00C',
    5 => 'P00I',
    6 => 'P01M',
    7 => 'P01MA',
    8 => 'P01T',
    9 => 'P01T 1',
    10 => 'P01V',
    11 => 'P01W',
    12 => 'P01Y',
    13 => 'P01Z',
    14 => 'P021',
    15 => 'P022',
    16 => 'P023',
    17 => 'P024',
    18 => 'P027',
    19 => 'P028',
  ),
  '@P1' => 
  array (
    0 => 'P10AN',
    1 => 'P105',
    2 => 'P1m',
    3 => 'P1ma40',
    4 => 'P1035X',
    5 => 'P1050X',
    6 => 'P1060X',
    7 => 'P1061X',
    8 => 'P10HD??(E6NA)',
    9 => 'P11HD',
    10 => 'P11HD四核(G9X6)',
    11 => 'P11HD??(G9X6)',
    12 => 'P11HD四核(G9X7)',
    13 => 'P19HD(E1K3)',
    14 => 'P19HD(E1K5)',
  ),
  '@P2' => 
  array (
    0 => 'P2A700',
    1 => 'P2Lite',
    2 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    3 => 'P20HD EEA',
  ),
  '@P3' => 
  array (
    0 => 'P330X',
    1 => 'P350X',
    2 => 'P360X',
    3 => 'P300(Funbook)',
  ),
  '@P4' => 
  array (
    0 => 'P4i',
    1 => 'P4',
    2 => 'P4D Sirius',
  ),
  '@P5' => 
  array (
    0 => 'P5 Energy',
    1 => 'P5Life',
    2 => 'P5Life TM',
    3 => 'P5',
  ),
  '@P6' => 
  array (
    0 => 'P6 Energy',
    1 => 'P6 Energy TM',
    2 => 'P6 Pro',
    3 => 'P6 Qmax',
    4 => 'P6 Quad',
    5 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
  ),
  '@P7' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    1 => 'P700i',
    2 => 'P701',
    3 => 'P7',
    4 => 'P70 3G八核(C2B7)',
    5 => 'P70h(A5C2)',
    6 => 'P72',
    7 => 'P72w',
    8 => 'P75a(G7E3)',
    9 => 'P75HD(M3E5)',
    10 => 'P76a(K3G5)',
    11 => 'P76a双核(M4Q5)',
    12 => 'P76e(A3E1)',
    13 => 'P76e(G6R8)',
    14 => 'P76h双核(K8V1)',
    15 => 'P76s双核(DKH5)',
    16 => 'P76TI',
    17 => 'P76v(A3V5)',
    18 => 'P78双核(G1M5)',
    19 => 'P78s(G1M5)',
    20 => 'P78s(G1M6)',
    21 => 'P78s(G1M7)',
    22 => 'P78HD四核(C4A5)',
    23 => 'P78HD??(C4A5)',
    24 => 'P79HD(A3V9)',
    25 => 'P79HD 3G(C4K5)',
    26 => 'p7901a',
  ),
  '@P8' => 
  array (
    0 => 'P8 Energy',
    1 => 'P8 Energy mini',
    2 => 'P8 Energy mini TM',
    3 => 'P80 四核 (BV8K)',
    4 => 'P80 四核 (BV9K)',
    5 => 'P80s 四核(EF6W)',
    6 => 'P80 3G四核 (B1KC)',
    7 => 'P80 3G四核 (B4KC)',
    8 => 'P80 3G八核(A4LL)',
    9 => 'P80 3G八核(A5LL)',
    10 => 'P80 3G八核(A6LL)',
    11 => 'P80 4G(K2G3)',
    12 => 'P81HD',
    13 => 'P85(A9D3)',
    14 => 'P85(A9D5)',
    15 => 'P85(R8A1)',
    16 => 'P85mini(DT8W)',
    17 => 'P85s mini(EWE8)',
    18 => 'P85t mini(KW8P)',
    19 => 'P85HD双核(K6N2)',
    20 => 'P86(F8G6)',
    21 => 'P88双核(H3D7)',
    22 => 'P88(H3D7)',
    23 => 'P88(GT4K)',
    24 => 'P88s mini??(F2V6)',
    25 => 'P88s mini四核(F2V7)',
    26 => 'P88HD(C5GV)',
    27 => 'P89 3G八核(D1H8)',
    28 => 'P89 3Gk8(D1H8)',
    29 => 'P89mini(E2W6)',
    30 => 'P89 mini(E2W6)',
    31 => 'P89s(F8A5)',
    32 => 'P89s mini(F8A2)',
  ),
  '@P9' => 
  array (
    0 => 'P9 Energy',
    1 => 'P940',
    2 => 'P990',
    3 => 'P90(H3U6)',
    4 => 'P90(H3U7)',
    5 => 'P98(F2K9)',
    6 => 'P98 八核(B9A3)',
    7 => 'P98 3Gk8(A4HY)',
    8 => 'P98 3G??(A4HY)',
    9 => 'P98 3G(M1Q2)',
    10 => 'P98 3G八核(A3HY)',
    11 => 'P98 3G八核(A4HY)',
    12 => 'P98 4G??(A8H6)',
    13 => 'P98 Air?? (C6V8)',
    14 => 'P98Air(C6V8)',
    15 => 'P98HD(A2Q6)',
    16 => 'P98HD+M6',
    17 => 'P98T(K3C4)',
  ),
  '@PA' => 
  array (
    0 => 'Panda(Board)?!',
    1 => 'Passion',
    2 => '(Transformer )?(Pad )?TF300(T|TG|TL)!',
    3 => '(Transformer )?(Pad )?TF502(T)!',
    4 => '(Transformer )?(Pad )?TF700(T|K)!',
    5 => 'PadFone',
    6 => 'PadFone 2',
    7 => 'PadFone Infinity',
    8 => 'PadFone Infinity A86',
    9 => 'PadFone T004',
    10 => 'PadFone-T004',
    11 => 'PadFone T008',
    12 => 'PadFone T00C',
    13 => 'PadFone X',
    14 => 'PadFone X mini',
    15 => 'Patio100',
    16 => 'Patio100 3G',
    17 => 'PAD[0-9]{3,3}!!',
    18 => '(HW-|HUAWEI )?PAR-(AL00|LX1|LX1M|LX9|TL00)!',
    19 => 'PADM00',
    20 => 'PADT00',
    21 => 'PAAM00',
    22 => 'PACM00',
    23 => 'PACT00',
    24 => 'PAAT00',
    25 => 'PAFM00',
    26 => 'PAHM00',
    27 => 'Panasonic!!',
    28 => 'pandigital9hr',
    29 => 'pandigital9hr2',
    30 => 'pandigitalopc1',
    31 => 'pandigitalopp1',
    32 => 'pandigitalp1hr',
    33 => 'PantechP!!',
    34 => 'PAT712W',
    35 => 'PATG7506HD',
    36 => 'PAP[0-9]{4,4}!!',
    37 => 'PAD1001',
  ),
  '@PB' => 
  array (
    0 => 'PB99400',
    1 => 'PBAM00',
    2 => 'PBAT00',
    3 => 'PBFT00',
    4 => 'PBBM00',
    5 => 'PBCM30',
    6 => 'PBEM00',
    7 => 'PBET00',
    8 => 'PBDM00',
  ),
  '@PC' => 
  array (
    0 => 'PC1038',
    1 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
    2 => '(HTC|PCD|USCC)?ADR[0-9]{4,4}!!',
    3 => 'PC36100!',
    4 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    5 => 'PC-TE307N1W',
    6 => 'PC-TE508BAW',
    7 => 'PC-TS508FAM',
    8 => 'PC-TS508T1W',
    9 => 'PC-TS708T1W',
    10 => 'PCGM00',
    11 => 'PCAM00',
    12 => 'PCAM10',
    13 => 'PCCM00',
  ),
  '@PD' => 
  array (
    0 => 'PD10-DYD',
    1 => 'PD6D1J',
  ),
  '@PE' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    1 => 'PEDI',
    2 => 'Penta!!',
    3 => 'Pentagram!!',
    4 => 'pepper',
  ),
  '@PF' => 
  array (
    0 => '(DNS )?(Airtab )?(E|ES|M|MA|MC|MF|MW|P|PC|PF)[0-9]{2,4}!!',
  ),
  '@PG' => 
  array (
    0 => 'PG06100',
    1 => 'PG41200',
    2 => 'PG86100!',
    3 => 'PGM 398',
    4 => 'PGPS7795',
  ),
  '@PH' => 
  array (
    0 => 'PH7M EU 5596',
    1 => 'PHA-3850',
    2 => 'PHA-3880',
    3 => 'PHA-4850',
    4 => 'PHA-5850',
    5 => 'PH-1',
    6 => 'photon',
    7 => 'PH44100',
    8 => 'Photon',
    9 => 'Photon 4G',
    10 => 'Photon Q',
    11 => 'Photon Q LTE',
    12 => 'PH350',
    13 => 'PH520',
    14 => 'PHICOMM!!',
    15 => 'Philips I908',
    16 => 'Philips I928',
    17 => 'Philips S301',
    18 => 'Philips S307',
    19 => 'Philips S308',
    20 => 'Philips S309',
    21 => 'Philips S316T',
    22 => 'Philips S326',
    23 => 'Philips S337',
    24 => 'Philips S358',
    25 => 'Philips S388!',
    26 => 'Philips S396',
    27 => 'Philips S398',
    28 => 'Philips S616',
    29 => 'Philips T3500',
    30 => 'Philips T3566',
    31 => 'Philips V377',
    32 => 'Philips V387',
    33 => 'Philips V526',
    34 => 'Philips V787',
    35 => 'Philips Xenium V787',
    36 => 'Philips X588',
    37 => 'Philips W336',
    38 => 'Philips W536',
    39 => 'Philips W626',
    40 => 'Philips W632',
    41 => 'Philips W635',
    42 => 'PhilipsW635',
    43 => 'Philips W737',
    44 => 'Philips W832',
    45 => 'Philips W3500',
    46 => 'Philips W3509',
    47 => 'Philips W3550',
    48 => 'Philips W3568',
    49 => 'Philips W6350',
    50 => 'PhilipsW6350',
    51 => 'Philips W6610',
    52 => 'Philips W6618',
    53 => 'Philips W7555',
    54 => 'Philips-W7555',
    55 => 'Philips-W8500',
    56 => 'Philips W8510',
    57 => 'Philips W8555',
    58 => 'Philips W8560',
    59 => 'Philips GoGear Connect',
    60 => 'Philips PI5000',
    61 => 'PHABLET 4S',
    62 => 'Phablet 4.5Q',
    63 => 'Phablet 5,3 Q',
  ),
  '@PI' => 
  array (
    0 => 'Pixel C',
    1 => 'Pixel',
    2 => 'Pixel XL',
    3 => 'Pixel 2',
    4 => 'Pixel 2XL',
    5 => 'Pixel 2 XL',
    6 => 'Pixel 3',
    7 => 'Pixel 3 XL',
    8 => 'Pixel 3a',
    9 => 'Pixel 3a XL',
    10 => 'Pixel 4',
    11 => 'Pixel 4a',
    12 => 'Pixel 4a (5G)',
    13 => 'Pixel 4 XL',
    14 => 'Pixel 5',
    15 => 'Pixel 5a',
    16 => 'Pixel 6',
    17 => 'Pixel 6 Pro',
    18 => '(Axioo[\\- ])?PICO!!',
    19 => 'picoBit-L',
    20 => '(GIO-)?(GiONEE[- ])?Pioneer P1!',
    21 => '(GIO-)?(GiONEE[- ])?Pioneer P2!',
    22 => '(GIO-)?(GiONEE[- ])?Pioneer P3!',
    23 => '(GIO-)?(GiONEE[- ])?Pioneer P4!',
    24 => '(HW-|HUAWEI )?PIC-(LX9)!',
    25 => 'PixelV1',
    26 => 'Pixel V2',
    27 => 'Pixel V2+',
    28 => 'PI2000',
    29 => 'PI2010',
    30 => 'PI2011',
    31 => 'PI3100',
    32 => 'PI3100-93',
    33 => 'PI3100Z3 93',
    34 => 'PI3105',
    35 => 'PI3106',
    36 => 'PI3110',
    37 => 'PI3205G',
    38 => 'PI3210G',
    39 => 'PI3900',
    40 => 'PI3900-93',
    41 => 'PI3910',
    42 => 'PI4010G',
    43 => 'PI7000',
    44 => 'PI7100 93',
    45 => '(PNR-)?Pioneer!!',
    46 => 'PIRANHA!!',
  ),
  '@PJ' => 
  array (
    0 => 'PJ83100',
    1 => 'PJ35100',
  ),
  '@PL' => 
  array (
    0 => 'PLE-701L',
    1 => 'PLE-703L',
    2 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    3 => 'Planet II v2',
    4 => 'PLANET',
    5 => 'Playboy PB-S3',
    6 => '(PLOYER-)?MOMO!!',
    7 => 'PlayTabPro',
    8 => 'PLT!!',
    9 => 'PlayBook',
    10 => 'Plus E',
    11 => 'Play 6X-1000',
    12 => 'Play8X-1100',
  ),
  '@PM' => 
  array (
    0 => 'PM36100',
    1 => 'PM63100',
    2 => 'PMID!!',
    3 => 'PMP[0-9]{4,4}!!',
    4 => 'PMT[0-9]{4,4}!!',
  ),
  '@PN' => 
  array (
    0 => 'pnx8473 kiryung',
    1 => 'PNDPP4MT9G2',
    2 => 'PNDPP44QC7',
    3 => 'PNDPP48GP',
    4 => 'PNDPP4MT9G3',
    5 => 'PNDPP410GP',
    6 => '(PNR-)?Pioneer!!',
  ),
  '@PO' => 
  array (
    0 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    1 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    2 => 'PocketBook A7',
    3 => 'PocketBook A10',
    4 => 'PocketBook A10 3G',
    5 => 'PocketBook SURFpad2',
    6 => 'PocketBook SURFpad 3 (7,85")',
    7 => 'PocketBook SURFpad 4 S',
    8 => 'PocketBook SURFpad 4 M',
    9 => 'PocketBook SURFpad 4 L',
    10 => 'POV-Mobii-7',
    11 => 'POV Mobii 7',
    12 => 'POV TAB-P506!',
    13 => 'POV TAB-P517!',
    14 => 'POV TAB-P527!',
    15 => 'POV TAB-P629!',
    16 => 'POV TAB-P701!',
    17 => 'POV TAB-P703!',
    18 => 'POV TAB-P720!',
    19 => 'POV TAB-P721A!',
    20 => 'POV TAB-P721!',
    21 => 'POV TAB-P722C!',
    22 => 'POV TAB-P731N!',
    23 => 'POV TAB-P825!',
    24 => 'POV TAB-P925!',
    25 => 'POV TAB-PR945!',
    26 => 'POV TAB-P1025!',
    27 => 'POV TAB-P1030!',
    28 => 'POV TAB-PI1045!',
    29 => 'POV TAB-P1325!',
    30 => '(POV )?TAB-PLAYTABPRO!',
    31 => '(POV )?TAB-PROTAB25!',
    32 => '(POV )?TAB-PROTAB26!',
    33 => 'POV TAB-PROTAB27',
    34 => '(POV )?TAB-PROTAB30!',
    35 => '(POV )?TAB-PROTAB2XXL(4)!',
    36 => '(POV )?TAB NAVI7 3G M!',
    37 => 'POV_TAB-NAVI7-3G-M',
    38 => 'POV TAB-P547(v1.0)',
    39 => 'POV TV-HDMI-200BT',
    40 => 'POV TV-HDMI-200BT(V2.0)',
    41 => 'POV TV-HDMI-210',
    42 => 'POV TV-HDMI-KB-01',
    43 => 'POV TV-SMARTTV-500',
    44 => 'Polaroid PSPT401',
    45 => 'Polaroid PSPC505',
    46 => 'Polaroid PSPC550',
    47 => 'Polaroid P4005A',
    48 => 'Polaroid P5005A',
    49 => 'Polaroid P5046A',
    50 => 'Polaroid P5525A',
    51 => 'Polaroid P5526A',
    52 => 'POLY ?PAD!!',
    53 => 'POLYTRON!!',
    54 => 'POMP!!',
    55 => 'Positivo Mini',
    56 => 'POSITIVO YPY 07FTB',
    57 => 'Positivo Ypy AB7E',
    58 => 'Positivo Ypy AB7EC',
    59 => 'Positivo Ypy AB7F',
    60 => 'Positivo AB7F',
    61 => 'Positivo Ypy AB7H',
    62 => 'Positivo Ypy AB10E',
    63 => 'Positivo Ypy AB10EC',
    64 => 'Positivo Ypy AB10H',
    65 => 'Positivo BGH Ypy L700',
    66 => 'Positivo Ypy L700',
    67 => 'Positivo Ypy L700 Ed. Especial',
    68 => 'Positivo Ypy L700+',
    69 => 'Positivo Ypy L700 Kids',
    70 => 'Positivo BGH Ypy L700 Kids',
    71 => 'Positivo Ypy L700+ Kids',
    72 => 'Positivo Ypy L1000',
    73 => 'Positivo Ypy L1000AB',
    74 => 'Positivo Ypy L1000F',
    75 => 'Positivo Ypy L1050',
    76 => 'Positivo Ypy L1050E',
    77 => 'Positivo Ypy L1050F',
    78 => 'Positivo S380',
    79 => 'Positivo S440',
    80 => 'Positivo S480',
    81 => 'Positivo S550',
    82 => 'Positivo X400',
    83 => 'Positivo X800',
    84 => 'Power Armor 13',
    85 => 'POCOPHONE F1',
    86 => 'POCO F1',
    87 => 'POCO F2 Pro',
    88 => 'POCO X2',
    89 => 'POCO X3 NFC',
    90 => 'POCO X3 Pro',
  ),
  '@PP' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    1 => 'PP4MT-7',
    2 => 'PP4MT-9',
  ),
  '@PR' => 
  array (
    0 => 'Project Tango Tablet Development Kit',
    1 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    2 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    3 => 'Pro 10 dual core',
    4 => 'Prada 3.0',
    5 => 'PRO 5',
    6 => 'PRO 6',
    7 => 'PRO 6s',
    8 => 'PRO 6 Plus',
    9 => 'Primo76',
    10 => 'Primo 91',
    11 => 'PRIME PLUS 3G',
    12 => 'PROTAB2XXL',
    13 => 'PROV!!',
    14 => 'PRO!!',
    15 => 'Prestigio PAP5000TDUO',
    16 => 'PRBT 100',
    17 => 'printer-tablet',
    18 => 'PRS-T1',
    19 => 'Preo Teknosa P1',
    20 => 'PRIMO7',
    21 => 'PRIMO8',
    22 => 'PRO7D',
    23 => 'Primo C2',
    24 => 'Primo C3 3G',
    25 => 'Primo C3',
    26 => 'PrimoC4',
    27 => 'Primo-D1',
    28 => 'Primo D2',
    29 => 'Primo D3',
    30 => 'Primo D4',
    31 => 'Primo D5',
    32 => 'Primo D6',
    33 => 'Primo D7',
    34 => 'Primo E1',
    35 => 'Primo E2',
    36 => 'PRIMO E3',
    37 => 'Primo E4',
    38 => 'Primo E4+',
    39 => 'Primo E5',
    40 => 'Primo E6',
    41 => 'Primo E7',
    42 => 'Primo E7 Plus',
    43 => 'Primo EF',
    44 => 'Primo EF2',
    45 => 'Primo EF3',
    46 => 'Primo EF4',
    47 => 'Primo EF4+',
    48 => 'Primo EM',
    49 => 'Primo F1',
    50 => 'Primo F2',
    51 => 'Primo-F2',
    52 => 'Primo F3',
    53 => 'Primo F3i',
    54 => 'Primo F4',
    55 => 'Primo F5',
    56 => 'Primo F6',
    57 => 'Primo F7',
    58 => 'Primo-G1',
    59 => 'Primo G1',
    60 => 'Primo G2',
    61 => 'Primo-G3',
    62 => 'Primo G4',
    63 => 'Primo G5',
    64 => 'Primo G6',
    65 => 'Primo-GF',
    66 => 'Primo GF2',
    67 => 'Primo GF3',
    68 => 'Primo GF4',
    69 => 'Primo GH',
    70 => 'Primo GH+',
    71 => 'Primo-GH2',
    72 => 'Primo GH2',
    73 => 'Primo GH2 [AOSP]',
    74 => 'Primo-GH2 (AOSP KITKAT)',
    75 => 'Primo GH3',
    76 => 'Primo-GH3',
    77 => 'Primo GH4',
    78 => 'Primo GH5',
    79 => 'Primo GH5 Plus',
    80 => 'Primo GH5 mini',
    81 => 'Primo GH6',
    82 => 'Primo GH6+',
    83 => 'Primo GM',
    84 => 'Primo GM mini',
    85 => 'Primo H2',
    86 => 'Primo-H3',
    87 => 'Primo H3',
    88 => 'Primo H4',
    89 => 'Primo H5',
    90 => 'Primo H6',
    91 => 'Primo HM',
    92 => 'Primo HM2',
    93 => 'Primo HM3',
    94 => 'Primo HM3+',
    95 => 'Primo-N',
    96 => 'Primo-N1',
    97 => 'Primo N2',
    98 => 'Primo NF',
    99 => 'Primo NF+',
    100 => 'Primo NF2',
    101 => 'Primo NF2+',
    102 => 'Primo NH',
    103 => 'Primo NH Lite',
    104 => 'Primo NH2 Lite',
    105 => 'Primo NH3 Lite',
    106 => 'Primo NX',
    107 => 'Primo NX2',
    108 => 'Primo NX3',
    109 => 'Primo NX3 Plus',
    110 => 'Primo NX4 mini',
    111 => 'Primo R1',
    112 => 'Primo R2',
    113 => 'Primo R3',
    114 => 'Primo R4',
    115 => 'Primo R4 Plus',
    116 => 'Primo R4s',
    117 => 'Primo RH',
    118 => 'Primo RH2',
    119 => 'Primo RM',
    120 => 'Primo RM2',
    121 => 'Primo RM2 mini',
    122 => 'Primo RX',
    123 => 'Primo RX2',
    124 => 'PrimoRX2',
    125 => 'Primo RX3',
    126 => 'Primo RX4',
    127 => 'Primo RX5',
    128 => 'Primo S1',
    129 => 'PrimoS2',
    130 => 'Primo S3',
    131 => 'Primo S3 mini',
    132 => 'Primo S4',
    133 => 'Primo V1',
    134 => 'Primo V2',
    135 => 'Primo VX',
    136 => 'Primo VX+',
    137 => '(WALTON )?Primo-X1!',
    138 => 'Primo-X2',
    139 => 'Primo X2mini',
    140 => 'Primo X3',
    141 => 'Primo X3mini',
    142 => 'Primo X4',
    143 => 'Primo Z',
    144 => 'Primo-ZX',
    145 => 'Primo ZX2',
    146 => 'Primo ZX2 Lite',
    147 => 'Primo ZX2 mini',
    148 => 'Primo Walpad 1',
    149 => 'Primo Walpad 7',
    150 => 'Primo Walpad 8',
    151 => 'Primo Walpad 8W',
    152 => 'Proton Lite',
    153 => 'prada',
    154 => 'Prism',
    155 => 'Prism II',
  ),
  '@PS' => 
  array (
    0 => 'PSPT350',
    1 => 'PSP[0-9]{4,4}!!',
    2 => 'PS47',
  ),
  '@PT' => 
  array (
    0 => 'PTL21',
  ),
  '@PU' => 
  array (
    0 => 'PURE XL',
    1 => 'PURE III',
    2 => 'PULID F6',
    3 => 'PULID F7',
    4 => 'PULID F11',
    5 => 'PULID T3',
    6 => 'PULP',
    7 => 'PULP 4G',
    8 => 'Pulse',
    9 => 'Pulse Mini',
  ),
  '@PY' => 
  array (
    0 => 'Pyramid',
  ),
  '@Q-' => 
  array (
    0 => 'Q-Wave!!',
    1 => '(QMobile|QTab|Q-Smart|Noir)!!',
  ),
  '@Q0' => 
  array (
    0 => 'Q07CL01',
  ),
  '@Q1' => 
  array (
    0 => '(BB )?Q10$!',
  ),
  '@Q5' => 
  array (
    0 => '(BB )?Q5$!',
  ),
  '@QI' => 
  array (
    0 => 'Qilive!!',
  ),
  '@QK' => 
  array (
    0 => 'QK1505',
    1 => 'QK1505 A01',
    2 => 'QK1605-A01',
  ),
  '@QM' => 
  array (
    0 => 'QM151E',
    1 => 'QM152E',
    2 => 'QM163E',
    3 => 'QM734-8G',
    4 => 'QM735-8G',
    5 => '(QMobile|QTab|Q-Smart|Noir)!!',
    6 => 'QMV7A',
    7 => 'QMV7B',
  ),
  '@QO' => 
  array (
    0 => 'QOOQ',
  ),
  '@QS' => 
  array (
    0 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
  ),
  '@QT' => 
  array (
    0 => '(QMobile|QTab|Q-Smart|Noir)!!',
    1 => 'QTAQZ3',
    2 => 'QTAQZ3KID',
    3 => 'QTAIR7',
  ),
  '@QU' => 
  array (
    0 => 'Quest',
    1 => 'Quicki[ -]?([0-9]+)!',
    2 => 'QUANTUM 4',
    3 => 'QUANTUM 700m',
    4 => 'QUANTUM 1010N',
    5 => 'Quechua Phone 5',
    6 => 'QUBE[ -](B[0-9]S?\\+?)!',
    7 => 'QUBE[ -](T[0-9])!',
    8 => 'QUMO[ -]QUEST[ -]?([0-9]+i?)!',
    9 => 'QUMO Altair ([0-9]+i?)!',
    10 => 'QUMO Vega ([0-9]+i?)!',
    11 => 'QuickFone Mini',
    12 => 'QuickFone-Mini',
    13 => 'QuickFone ([NXZ][0-9]\\+?)!',
  ),
  '@QV' => 
  array (
    0 => 'QV151E',
  ),
  '@QW' => 
  array (
    0 => 'QW ?TB-[0-9]{4,4}!!',
  ),
  '@R1' => 
  array (
    0 => 'R1 HD',
    1 => 'R1001',
    2 => 'R1011',
  ),
  '@R2' => 
  array (
    0 => 'R2001',
    1 => 'R2010',
    2 => 'R2017',
  ),
  '@R3' => 
  array (
    0 => 'R30GT',
  ),
  '@R6' => 
  array (
    0 => 'R6006',
    1 => 'R6007',
  ),
  '@R7' => 
  array (
    0 => 'R7005',
    1 => 'R7007',
    2 => 'R7f',
    3 => 'R7i',
    4 => 'R7kf',
    5 => 'R7sf',
    6 => 'R7t',
    7 => 'R7Plus',
    8 => 'R7plusf',
    9 => 'R7Plusm',
  ),
  '@R8' => 
  array (
    0 => 'R801',
    1 => 'R801T',
    2 => 'R803',
    3 => 'R805',
    4 => 'R807',
    5 => 'R809T',
    6 => 'R811',
    7 => 'R813T',
    8 => 'R815',
    9 => 'R815T',
    10 => 'R815W',
    11 => 'R817',
    12 => 'R817T',
    13 => 'R819',
    14 => 'R819T!',
    15 => 'R821',
    16 => 'R821T',
    17 => 'R823T',
    18 => 'R827',
    19 => 'R827T',
    20 => 'R829',
    21 => 'R829T',
    22 => 'R830',
    23 => 'R830S',
    24 => 'R831K',
    25 => 'R831L',
    26 => 'R831T',
    27 => 'R831S',
    28 => 'R831',
    29 => 'R833T',
    30 => 'R850',
    31 => 'R883T',
    32 => 'R8000',
    33 => 'R8001',
    34 => 'R8006',
    35 => 'R8007',
    36 => 'R8015',
    37 => 'R8106',
    38 => 'R8107',
    39 => 'R8109',
    40 => 'R8111',
    41 => 'R8113',
    42 => 'R8200',
    43 => 'R8201',
    44 => 'R8205',
    45 => 'R8206',
    46 => 'R8207',
    47 => 'R800(a|at|i|iv|x)!',
  ),
  '@RA' => 
  array (
    0 => 'Rage',
    1 => 'RAPAXSE080-0508',
    2 => 'RAZR',
    3 => 'RAZR HD',
    4 => 'RAZR M',
    5 => 'RAZR MAXX',
    6 => 'ramos i7s  ',
    7 => 'Ramos i7s Series',
    8 => 'Ramosi8',
    9 => 'Ramosi8c',
    10 => 'Ramosi9',
    11 => 'Ramosi9-3G',
    12 => 'Ramos i9s',
    13 => 'Ramos i9s Series',
    14 => 'Ramos i9s pro',
    15 => 'Ramosi10',
    16 => 'Ramos i11 pro',
    17 => 'Ramosi12c',
    18 => 'Ramos i100 pro',
    19 => 'Ramos K100',
    20 => 'Ramos K300',
    21 => 'Ramos M7',
    22 => 'ramos R9',
    23 => 'Ramos W6HD',
    24 => 'Ramos W12',
    25 => 'Ramos W12HD',
    26 => 'Ramos W17Pro V3.0',
    27 => 'Ramos W21',
    28 => 'Ramos W25HD',
    29 => 'Ramos W27Pro',
    30 => 'Ramos W31',
    31 => 'Ramos w36',
    32 => 'Ramos W41',
    33 => 'Ramos W41 QuadCore',
    34 => 'Ramos X10 PRO!',
    35 => 'Rayhov!!',
    36 => 'RAINBOW',
    37 => 'RAINBOW 4G',
    38 => 'RAINBOW JAM',
    39 => '(ZTE )?Racer!!',
  ),
  '@RB' => 
  array (
    0 => 'RBK-490',
  ),
  '@RC' => 
  array (
    0 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
    1 => 'RCT6703W12',
    2 => 'RCT6203W46',
    3 => 'RCT6203W46L',
    4 => 'RCT6223W87',
    5 => 'RCT6303W87DK',
    6 => 'RCT6303W87M',
    7 => 'RCT6303W87M7',
    8 => 'RCT6K03W13',
    9 => 'RCT6603W47M7',
    10 => 'RCT6603W87M7',
    11 => 'RCT6873W42',
    12 => 'RCT6773W22B',
    13 => 'RCT6773W22BM',
    14 => 'RCT6973W43',
    15 => 'RCT6513W87',
    16 => 'RCT6213W87DK',
    17 => 'RCT6673W23M',
    18 => 'RCT6673W43M',
    19 => 'RCT6S03W12',
  ),
  '@RE' => 
  array (
    0 => 'redhookbay',
    1 => '(E-Boda|Eruption|Essential|Supreme|Storm|Revo)!!',
    2 => 'Rezound',
    3 => 'Renesas!',
    4 => 'Readboy!!',
    5 => 'ReederA8R',
    6 => 'reeder A8i Quad 2',
    7 => '(Xiaomi )?(Redmi|RedRice|HM)!!',
    8 => '(Xiaomi )?(Redmi|HM)[ \\-]?Note!!',
    9 => 'REVVLPLUS C3701A',
  ),
  '@RG' => 
  array (
    0 => 'RG650',
    1 => 'RG655',
    2 => 'RG725',
    3 => 'RG850',
  ),
  '@RH' => 
  array (
    0 => '(?:RIVO )?RHYTHM RX ?([0-9]+)!',
  ),
  '@RI' => 
  array (
    0 => 'Rikomagic MK802',
    1 => 'Rikomagic MK802 ?II!',
    2 => 'Rikomagic MK802III',
    3 => 'Rikomagic MK802IIIS',
    4 => '(Gsmart|Gigabyte|Rio)!!',
    5 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    6 => 'Rise',
    7 => '(Ritmix )?RM[DP]-?[0-9]{3,3}!!',
    8 => 'RIDGE',
    9 => 'RIDGE 4G',
    10 => 'RIDGE FAB 4G',
  ),
  '@RK' => 
  array (
    0 => 'rksdk',
    1 => 'rk2808(sdk)?!',
    2 => 'rk2818(sdk)?!',
    3 => 'RK2906',
    4 => 'RK2918',
    5 => 'rk2926-q8',
    6 => 'rk2928sdk',
    7 => 'rk29sdk',
    8 => 'rk30sdk!',
    9 => 'rk30mtk',
    10 => 'rk3026',
    11 => 'rk3028a',
    12 => 'rk3028sdk',
    13 => 'rk3036',
    14 => 'rk3066',
    15 => 'rk31sdk',
    16 => 'rk31 5931',
    17 => 'rk31 8089',
    18 => 'rk31 872xu',
    19 => 'rk31au',
    20 => 'rk31rtl',
    21 => 'rk312x',
    22 => 'rk3188!',
    23 => 'rk3288!',
    24 => 'rk3990!',
    25 => 'RKM MK602',
    26 => 'RKM MK802IIIS',
    27 => 'RKM MK802IV',
    28 => 'RKM MK902',
    29 => 'RKM MK902II',
  ),
  '@RM' => 
  array (
    0 => 'RM02',
    1 => 'RM-980',
    2 => 'RM-1061',
    3 => '(Ritmix )?RM[DP]-?[0-9]{3,3}!!',
    4 => 'RMD-[0-9]{2,3}G!!',
    5 => 'RMX1911',
    6 => 'RMX1931',
    7 => 'RMX1971',
    8 => 'RMX1993',
    9 => 'RMX2001',
    10 => 'RMX2002',
    11 => 'RMX2020',
    12 => 'RMX2030',
    13 => 'RMX2063',
    14 => 'RMX2075',
    15 => 'RMX2086',
    16 => 'RMX2111',
    17 => 'RMX2144',
    18 => 'RMX2155',
    19 => 'RMX2170',
    20 => 'RMX2185',
    21 => 'RMX2202',
    22 => 'RMX3081',
    23 => 'RMX3085',
    24 => 'RMX3201',
    25 => 'RMX3241',
    26 => 'RMX3242',
    27 => 'RMX3263',
    28 => 'RMX3301',
    29 => 'RMX3311',
    30 => 'RMX3363',
    31 => 'RMX3370',
  ),
  '@RN' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@RO' => 
  array (
    0 => 'Rock-(AL00|CL00|L01|L03)!',
    1 => 'roiX',
    2 => 'RoverPad 3W T71D',
    3 => 'ROAR',
    4 => 'Roar A50',
    5 => 'Roar V25',
    6 => 'rolex',
  ),
  '@RP' => 
  array (
    0 => 'RP-UDM01A',
  ),
  '@RS' => 
  array (
    0 => 'RS988',
  ),
  '@RT' => 
  array (
    0 => 'RTC-700A',
  ),
  '@RW' => 
  array (
    0 => 'RW107',
  ),
  '@S-' => 
  array (
    0 => 'S-F16',
  ),
  '@S0' => 
  array (
    0 => 'S01',
  ),
  '@S1' => 
  array (
    0 => '(GIO-)?(GiONEE[- ])?S101!',
    1 => '(Lenovo )?S1[- ]37AH0!',
    2 => 'S1005KTAB',
  ),
  '@S2' => 
  array (
    0 => 'S222',
    1 => '(Lenovo )?S2[- ]38A(H0|T0)!',
    2 => 'S2',
  ),
  '@S3' => 
  array (
    0 => 's3c6410',
    1 => 'S30',
    2 => 'S31',
    3 => 'S308',
    4 => 'S3',
  ),
  '@S4' => 
  array (
    0 => 'S40',
    1 => 'S41',
    2 => 'S42',
    3 => 'S4503Q',
    4 => 'S4505M',
    5 => 'S4',
  ),
  '@S5' => 
  array (
    0 => 'S5PV210',
    1 => 'S5-F',
    2 => 'S50',
    3 => 'S50c',
    4 => 'S52',
    5 => 'S58Pro',
    6 => 'S5 Pro',
    7 => 'S5',
    8 => 'S5 Mini!',
    9 => 'S51SE',
  ),
  '@S6' => 
  array (
    0 => 'S61',
    1 => 'S62 Pro',
    2 => 'S68Pro',
    3 => 'S658t',
    4 => 'S6 Edge+!',
  ),
  '@S7' => 
  array (
    0 => 'S70Lite',
    1 => 'S710d',
    2 => 'S710D',
    3 => 'S720i',
    4 => 'S7',
    5 => 's732',
  ),
  '@S8' => 
  array (
    0 => 'S88Pro',
    1 => 'S8600',
    2 => 'S88 DISCOVERY',
    3 => 'S850',
    4 => 'S880',
  ),
  '@S9' => 
  array (
    0 => 'S96Pro',
  ),
  '@SA' => 
  array (
    0 => 'sama5d3',
    1 => 'SABRESD-MX6DQ',
    2 => 'saltbay',
    3 => 'Samsung Chromebook 3',
    4 => 'SA3CNT',
    5 => 'Salora E1',
    6 => 'Salora-E1',
    7 => 'Salora-E4',
    8 => 'Salora-E5',
    9 => 'SALORA-E6',
    10 => 'SALORA-E7',
    11 => 'SALORA E8',
    12 => 'Sansui[ -]([A-Z]{1,2}[0-9]+[A-Z]?\\+?)!',
    13 => 'Sansui Cosmic SP ([0-9]+)!',
    14 => 'SANSUI ETAB ([0-9]+) ?VP!',
    15 => 'Satellite!!',
    16 => 'sagit',
    17 => 'santoni',
    18 => 'San Remo Mini',
    19 => 'San Francisco',
    20 => 'San Francisco for',
    21 => 'San Francisco II',
  ),
  '@SB' => 
  array (
    0 => 'SBM[0-9]0[0-9]SH!!',
  ),
  '@SC' => 
  array (
    0 => 'SCM-(AL09|W09)!',
    1 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    2 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    3 => '(HW-|HUAWEI )?(AMN|ART|AQM|CRO|LUA|CUN|DUB|SCC|SCU|CAM|LDN|LYO|MED|MYA)!!',
    4 => 'SCH-[iI][0-9]{3,3}!!',
    5 => 'SCH-L710!',
    6 => 'SCH-M828!',
    7 => 'SCH-N719',
    8 => 'SCH-P[0-9]{3,3}!!',
    9 => 'SCH-R[0-9]{3,3}!!',
    10 => 'SCH-S[0-9]{3,3}!!',
    11 => 'SCH-V727',
    12 => 'SCH-W[0-9]{3,4}!!',
    13 => 'SC-?0[0-9][A-Z]!!',
    14 => 'SC[LTV][23][0-9]!!',
    15 => 'Scroll!!',
    16 => 'SC-!!',
    17 => 'scorpio',
  ),
  '@SD' => 
  array (
    0 => 'sdk',
    1 => 'SD4930UR',
  ),
  '@SE' => 
  array (
    0 => 'sec smdkc210',
    1 => 'sec smdkv210',
    2 => 'SENSUELLE',
    3 => 'SensationXE!',
    4 => 'SensationXL!',
    5 => 'Sensation XL with Beats Audio',
    6 => 'Sensation!',
    7 => '(SENCOR )?ELEMENT!!',
    8 => 'SENSEIT R390',
    9 => 'SENWA S970',
  ),
  '@SF' => 
  array (
    0 => 'SFR-G8800',
    1 => 'SFR-G8800 TV',
    2 => 'SFR StarTab',
  ),
  '@SG' => 
  array (
    0 => 'SGH-[EILNMS][0-9]{3,4}!!',
    1 => 'SGH-T[0-9]{3,3}!!',
    2 => 'SGH-U468!',
    3 => 'SGH-W2013!',
    4 => 'SGP[ST0-9][0-9]{2,2}!!',
  ),
  '@SH' => 
  array (
    0 => 'Shark Bay Client platform',
    1 => 'shamu',
    2 => 'SHIELD Console',
    3 => 'SHIELD Android TV',
    4 => 'SHIELD Andr0id TV',
    5 => 'SHIELD',
    6 => 'shieldtablet',
    7 => 'SHIELD Tablet',
    8 => 'SHIELD Tablet K1',
    9 => 'SHIELD Tablet X1',
    10 => 'SHIELD Tablet II',
    11 => 'Shine',
    12 => 'SHT-W09',
    13 => 'SH940C-LN',
    14 => 'SH940C-LN TV',
    15 => 'SH960C-LN',
    16 => 'Sholes',
    17 => 'SHV-E[0-9]{3,3}!!',
    18 => 'SHW-M[0-9]{3,3}!!',
    19 => 'SH01D',
    20 => 'SH-A01',
    21 => 'SH-C02',
    22 => 'SH-D01',
    23 => 'SH-M01',
    24 => 'SH-M02',
    25 => 'SH-M02-EVA20',
    26 => 'SH-M03',
    27 => 'SH-M05',
    28 => 'SH-M07',
    29 => 'SH-RM02',
    30 => 'SH-Z01',
    31 => 'SH-[01][0-9][A-Z]!!',
    32 => '(SHP-)?(SHARP )?SH[0-9]{2,3}!!',
    33 => 'SH[FLTV][0-9]{2,2}!!',
    34 => 'SHARP-ADS1',
    35 => 'SHIFT6m',
    36 => 'SHARK PRS-H0',
  ),
  '@SI' => 
  array (
    0 => 'SiRF Dream',
    1 => 'simvalley SP-142',
    2 => 'Signature Touch',
    3 => 'Signature Touch L',
  ),
  '@SK' => 
  array (
    0 => 'SK351',
    1 => 'SKT01',
    2 => '(SKY )?IM[- ][A-Z][0-9]{3,3}!!',
    3 => 'Skyworth!!',
    4 => 'SK17(a|i|iv|i-o)!',
    5 => 'SK-Mtek GT7305',
    6 => 'SKR-H0',
    7 => '(ZTE )?Skate!!',
  ),
  '@SL' => 
  array (
    0 => 'Slider SL101',
    1 => 'SL930',
    2 => '(HP|Slate)!!',
    3 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    4 => 'Slidepad!!',
    5 => 'SLIDE',
  ),
  '@SM' => 
  array (
    0 => 'smdk6410',
    1 => 'SMDKC110',
    2 => 'SMDKV210',
    3 => 'SMDK4x12',
    4 => 'SMDK4412',
    5 => 'SMDK5250',
    6 => 'SMDK5410',
    7 => 'smp86xx',
    8 => 'smp8734',
    9 => 'smp8756',
    10 => 'SM-G900FG',
    11 => 'SmartWatch 3',
    12 => 'Smart!!',
    13 => 'SM-H900A',
    14 => 'SmartTab1',
    15 => 'SmartPad!!',
    16 => '(MEO )?Smart A!!',
    17 => 'Smart Monitor 17',
    18 => 'SMARTTVBOX',
    19 => 'SMT-E5015',
    20 => 'SMT-i9100',
    21 => 'SM-A[0-9]{3,3}!!',
    22 => 'SM-C[0-9]{3,3}!!',
    23 => 'SM-E[0-9]{3,3}!!',
    24 => 'SM-F[0-9]{3,3}!!',
    25 => 'SM-G[0-9]{3,3}!!',
    26 => 'SM-I9500',
    27 => 'SM-J[0-9]{3,3}!!',
    28 => 'SM-M[0-9]{3,4}!!',
    29 => 'SM-N[0-9]{3,4}!!',
    30 => 'SM-P[0-9]{3,3}!!',
    31 => 'SM-S[0-9]{3,3}!!',
    32 => 'SM-T[0-9]{3,3}!!',
    33 => 'SM-X200',
    34 => 'SM-X205',
    35 => 'SM-X700',
    36 => 'SM-X800',
    37 => 'SM-X900',
    38 => 'SM-X906!',
    39 => 'SM-V700',
    40 => 'SM-W[0-9]{4,4}!!',
    41 => 'Smartphone Android by SFR STARADDICT II',
    42 => '(Smartfren|Andromax)!!',
    43 => 'Smartfren Tab 7',
    44 => 'SM919',
    45 => 'SM701',
    46 => 'SM801',
    47 => 'SmartQ!!',
    48 => 'SMART Sprint',
    49 => 'SMART Start',
    50 => 'SMART Surf2 4G',
    51 => 'SmartTab!!',
  ),
  '@SN' => 
  array (
    0 => 'SN[0-9]!!',
    1 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    2 => 'Snexian!!',
    3 => '(SNM\\-)?LT[0-9]{2,2}[a-z]?!!',
    4 => '(SNM\\-)?M[0-9]{2,2}[a-z]!!',
  ),
  '@SO' => 
  array (
    0 => 'SoftwinerEvb',
    1 => 'SoftwinerEvb0308',
    2 => 'SoftwinerEvd',
    3 => 'Softwinerkf026',
    4 => 'Softwinerf721',
    5 => 'Softwinerf761',
    6 => 'Softwinerf761I',
    7 => 'Softwinerf900',
    8 => 'SoftwinerEvbV13',
    9 => 'soho',
    10 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    11 => 'SONIC',
    12 => 'Sonim!!',
    13 => 'SO-0[0-9][A-Z]!!',
    14 => 'SO[LTV][0-9]{2,2}!!',
    15 => 'Sony Xperia Ray',
    16 => 'SonyEricssonMT15',
    17 => 'SonyEricssonST15',
    18 => 'Soshphone 4G',
    19 => 'soft stone',
  ),
  '@SP' => 
  array (
    0 => 'Sprint!!',
    1 => 'Sparkle V',
    2 => 'SPNG9716DC',
    3 => 'SPH-D[0-9]{3,3}!!',
    4 => 'SPH-L[0-9]{3,3}!!',
    5 => 'SPH-M[0-9]{3,3}!!',
    6 => 'SPH-P[0-9]{3,3}!!',
    7 => 'SP-120',
    8 => 'SPX-5',
    9 => 'SPX-5 3G',
    10 => 'SPX-6',
    11 => 'SPX-12',
    12 => 'SpeedUp S3',
    13 => '(CSL[- ])?(Spice[- ]?)?Mi(-| )?[0-9]{3,3}!!',
    14 => 'Spice N-300',
    15 => 'Spice N-500',
    16 => 'Spice Xlife-425 3G',
    17 => 'Spice Xlife-435Q',
    18 => 'Spice Xlife-451Q',
    19 => 'Spice-Xlife-511Pro',
    20 => 'Spice Xlife-512',
    21 => 'Spice Xlife-520HD',
    22 => 'Spice Xlife-M5+',
    23 => 'Spice Xlife-M45q',
    24 => 'Spice Xlife-M46q',
    25 => 'Spice Xlife-Proton4',
    26 => 'Spice Xplor-Proton5',
    27 => 'SPICE IV',
    28 => 'Spro2',
    29 => 'SP-A20i',
    30 => 'SpringBoard',
  ),
  '@SR' => 
  array (
    0 => 'SRT!!',
  ),
  '@ST' => 
  array (
    0 => 'ste l8540!',
    1 => 'ste u8500',
    2 => 'ste u9540',
    3 => 'Stingray',
    4 => 'Stream',
    5 => 'Status',
    6 => 'Style',
    7 => 'STV100-[0-9]!',
    8 => 'STH100-[0-9]!',
    9 => '(BLU|DASH|LIFE|NEO|STUDIO|VIVO)!!',
    10 => 'streak7',
    11 => '(E-Boda|Eruption|Essential|Supreme|Storm|Revo)!!',
    12 => 'ST7001',
    13 => 'ST8000',
    14 => 'ST9001',
    15 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
    16 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    17 => 'ST10',
    18 => 'ST940I-UP',
    19 => 'STM[0-9]{3,3}H!!',
    20 => 'Stylo',
    21 => 'STARACTIVE',
    22 => 'STARACTIVE 2',
    23 => 'STARADDICT II',
    24 => 'STARADDICT II Plus',
    25 => 'STARADDICT III',
    26 => 'STARADDICT 4',
    27 => 'STARADDICT 5',
    28 => 'STARADDICT 6',
    29 => 'STARNAUTE II',
    30 => 'STARNAUTE 3',
    31 => 'STARNAUTE3',
    32 => 'STARNAUTE4',
    33 => 'Starshine',
    34 => 'STARSHINE II',
    35 => 'STARSHINE III',
    36 => 'STARSHINE 4',
    37 => 'STARSHINE5',
    38 => 'STARTEXT II',
    39 => 'STARTRAIL II',
    40 => 'StarTrail III',
    41 => 'STARTRAIL4',
    42 => 'STARTRAIL 4',
    43 => 'STARTRAIL5',
    44 => 'STARTRAIL 6 4G',
    45 => 'STARTRAIL6',
    46 => 'STARTRAIL7',
    47 => 'STARTRAIL 8',
    48 => 'STARTRAIL 9',
    49 => 'STARXTREM',
    50 => 'STARXTREM II',
    51 => 'STARXTREM3',
    52 => 'STARXTREM 4',
    53 => 'STARXTREM5',
    54 => 'STARXTREM 6',
    55 => 'STARTAB',
    56 => 'ST[0-9]{2,2}[a-z]?!!',
    57 => 'Star N8000',
    58 => 'Star N9500',
    59 => 'Star N9589',
    60 => 'Star Q9000',
    61 => 'STAR S5',
    62 => 'STAR S7589',
    63 => 'Starmobile!!',
    64 => '(Starway )?Andromeda!!',
    65 => 'STX!!',
    66 => 'STOREX LinkBox',
    67 => 'StarTab 715x',
    68 => 'ST[0-9]{5,5}-[0-9]!!',
    69 => 'ST-PAD',
    70 => 'ST-PAD2',
    71 => 'STAIRWAY',
    72 => 'StarTrail TT',
  ),
  '@SU' => 
  array (
    0 => 'Surfing TAB B 9.7 3G',
    1 => 'Surfing Tab C 3G',
    2 => '(E-Boda|Eruption|Essential|Supreme|Storm|Revo)!!',
    3 => '(Explay|X-tremer|ActiveD|Informer|Surfer)!!',
    4 => 'Surface Duo',
    5 => 'Surface Duo 2',
    6 => 'surnia',
    7 => 'SUGAR ([A-Z][0-9]?(?: Pro)?)!',
    8 => 'SUPRA (M[0-9]{2,3}[A-Z]{0,2})!',
    9 => 'SUBLIM',
    10 => 'SUNSET',
    11 => 'SUNSET2',
  ),
  '@SV' => 
  array (
    0 => 'sv8860',
    1 => 'SVP-DTV15',
  ),
  '@SW' => 
  array (
    0 => 'Swift',
    1 => 'Swift Plus',
    2 => 'Swift 2',
    3 => 'Swift 2 Plus',
    4 => 'Swift 2 X',
  ),
  '@SX' => 
  array (
    0 => 'SXZ-PD!!',
    1 => 'SX9701W',
  ),
  '@SY' => 
  array (
    0 => 'Sygnus',
    1 => 'SYTABEX7-2',
    2 => '(Symphony|Xplorer)!!',
    3 => 'Synrgic Uno M1',
  ),
  '@SZ' => 
  array (
    0 => 'SZJ-JS101',
    1 => 'SZENIO!!',
  ),
  '@T-' => 
  array (
    0 => 'T-07B',
    1 => 'T-800',
    2 => 'T-200',
    3 => 'T-Smart!!',
    4 => 'T-01C',
    5 => 'T-01D',
    6 => 'T-02D',
    7 => 'T-Mobile G1',
    8 => 'T-Mobile G2',
    9 => 'T-Mobile G2 Touch',
    10 => 'T-Mobile HTC G2',
    11 => 'T-Mobile myTouch',
    12 => 'T-Mobile myTouch 3G',
    13 => 'T-Mobile myTouch 3G Slide',
    14 => 'T-mobile my touch 3g slide',
    15 => 'T-Mobile Espresso',
    16 => 'T-Mobile myTouch 4G',
    17 => 'T-Mobile myTouch Q',
    18 => 'T-Mobile Opal',
    19 => 'T-Mobile Vivacity',
    20 => 'T-Hub2',
  ),
  '@T0' => 
  array (
    0 => 'T01',
    1 => 'T012',
    2 => 'T02',
    3 => 'T05',
    4 => 'T03',
    5 => 'T04',
    6 => 'T00C',
    7 => 'T00E',
    8 => 'T00N',
    9 => 'T00D',
    10 => 'T00S',
    11 => 'T00T',
    12 => 'T001',
    13 => 'T00I',
    14 => 'T00I-D',
    15 => 'T00F',
    16 => 'T00J',
    17 => 'T00J-D',
    18 => 'T00K',
    19 => 'T00P',
    20 => 'T00G',
    21 => 'T00Q',
  ),
  '@T1' => 
  array (
    0 => 'T1-B',
    1 => 'T1-D',
    2 => 'T1-E',
    3 => 'T1K Plus',
    4 => 'T1 7.0',
    5 => 'T11AD!',
  ),
  '@T2' => 
  array (
    0 => 'T20-Plus',
    1 => 'T20',
  ),
  '@T3' => 
  array (
    0 => 'T320a',
    1 => 'T30',
    2 => 'T3696',
    3 => 'T301',
  ),
  '@T6' => 
  array (
    0 => 'T6',
    1 => 'T671H',
  ),
  '@T7' => 
  array (
    0 => '(OPPO-?)?T703!',
    1 => 'T774H',
    2 => 'T775H',
    3 => 'T790Y',
    4 => 'T720 SE',
    5 => 'T760 from moage.com',
  ),
  '@T8' => 
  array (
    0 => 'T8[0-9]{3,3}!!',
  ),
  '@T9' => 
  array (
    0 => 'T9108',
    1 => 'T92',
    2 => 'T9199h',
    3 => 'T9[0-9]{3,3}!!',
    4 => 'T959',
    5 => 'T98 4G??(C6R2)',
  ),
  '@TA' => 
  array (
    0 => 'Tango',
    1 => 'TA272HUL',
    2 => 'Tablet P1801-T',
    3 => 'Tablet P1802-T',
    4 => '(DENVER-)?TA[CD]-[0-9]{4,5}!!',
    5 => 'Tablet-P27',
    6 => 'TA10CA3',
    7 => '(tablet )?fnac!!',
    8 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    9 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    10 => '(HW-|HUAWEI )?(BLL|CHC|TAG|KII)!!',
    11 => 'TA-FONE!!',
    12 => 'TAB1011',
    13 => 'TAB-1030',
    14 => '(Lenovo )?(Tab ?)?(2 ?)?[AS](7|8|10)!!',
    15 => 'TAB950',
    16 => 'TAB1062',
    17 => 'TAB192',
    18 => 'TA-10(07|29)!',
    19 => 'TA-10(84)!',
    20 => 'TA-10(20|28|32|38)!',
    21 => 'TA-10(24|27|44|53)!',
    22 => 'TA-10(61|75|81|88)!',
    23 => 'TA-10(00|03|21|25|33|39)!',
    24 => 'TA-10(43|50|54|68)!',
    25 => 'TA-10(41)!',
    26 => 'TA-10(62)!',
    27 => 'TA-10(04|12|52)!',
    28 => 'TA-10(99)!',
    29 => 'TA-11(03|16)!',
    30 => '(POV )?TAB-PLAYTABPRO!',
    31 => '(POV )?TAB-PROTAB25!',
    32 => '(POV )?TAB-PROTAB26!',
    33 => '(POV )?TAB-PROTAB30!',
    34 => 'TAB-PROTAB2-IPS9',
    35 => 'TAB-PROTAB2-IPS-3G',
    36 => 'TAB-PROTAB2-IPS-16',
    37 => 'TAB-PROTAB2.4',
    38 => 'TAB-PROTAB2XL',
    39 => 'TAB-PROTAB2.4XL',
    40 => 'TAB-PROTAB2XXL',
    41 => 'TAB-PROTAB2XXL(4)',
    42 => '(POV )?TAB-PROTAB2XXL(4)!',
    43 => '(POV )?TAB NAVI7 3G M!',
    44 => 'Tablet P',
    45 => 'Tablet S',
    46 => 'Tabra QAV801',
    47 => 'TAB 7 3G V8',
    48 => 'TAB 8 3G V8',
    49 => 'TAB9 3G',
    50 => 'TAB9 3G V8',
    51 => 'TAB 10 3G V16',
    52 => 'TAB 7i 3G',
    53 => 'TAB 10Q',
    54 => 'TAB09-410',
    55 => 'TAB10-410',
    56 => 'TAB07-485',
    57 => 'TAB07-200',
    58 => 'TAB9-200',
    59 => 'TAB10-201',
    60 => 'TAB210',
    61 => 'TAB220',
    62 => 'TAB224',
    63 => 'TAB250',
    64 => 'TAB275',
    65 => 'TAB260',
    66 => 'TAB264',
    67 => 'TAB360',
    68 => 'TAB364',
    69 => 'TAB410',
    70 => 'TAB411',
    71 => 'TAB420',
    72 => 'TAB424',
    73 => 'TAB450',
    74 => 'TAB460',
    75 => 'TAB461',
    76 => 'TAB464',
    77 => 'TAB465EUK',
    78 => 'TAB468',
    79 => 'TAB469',
    80 => 'TAB466EUK',
    81 => 'TAB467',
    82 => 'TAB462',
  ),
  '@TB' => 
  array (
    0 => 'TB-X103F',
    1 => 'TB07FTA',
    2 => 'TB100',
    3 => 'TB782B',
    4 => 'TBD753B',
    5 => 'TBDB763',
    6 => 'TBDB863',
    7 => 'TBDC1093',
    8 => 'TBDG734',
    9 => 'TBDG874',
    10 => 'TBDG1073',
    11 => 'TBQC1063',
  ),
  '@TC' => 
  array (
    0 => 'TCC8920 STB!',
    1 => 'TCC893X!',
    2 => 'TCC8935 HDMI!',
    3 => 'TCC8975 STB!',
    4 => 'TCC8920 EVM',
    5 => 'TCC8925 HDMI DONGLE',
    6 => 'TCC8930 STB EV',
    7 => 'TCC8930 STB1',
    8 => 'TCC8935 HDMI DONGLE',
    9 => 'TCL ONE TOUCH 990',
    10 => 'TC970',
    11 => 'TC970 (Wi-Fi)',
    12 => 'TC975',
    13 => 'TC55',
    14 => 'TC55CH',
    15 => 'TC70',
    16 => 'TC75',
    17 => 'TCL!!',
    18 => 'TC26',
  ),
  '@TD' => 
  array (
    0 => 'TD070VA1',
    1 => 'TD600',
    2 => 'TD-1010',
    3 => 'TD506',
  ),
  '@TE' => 
  array (
    0 => 'Telechips M801 Evaluation Board',
    1 => 'Telechips TCC8800 eMMC Evaluation Board',
    2 => 'Telechips TCC8800 Evaluation Board',
    3 => 'Telechips TCC8900 Evaluation Board',
    4 => 'Telechips TCC8900 Evaluation Board (US)',
    5 => 'Telechips TCC8902 Tablet-PC (DE)',
    6 => 'Telechips TCC9300 Evaluation Board',
    7 => 'Tegra!!',
    8 => 'TECNO!!',
    9 => 'Teclast A10',
    10 => 'Teclast P76e!',
    11 => 'Teclast P85!',
    12 => 'Teclast X80h',
    13 => 'tegav2',
    14 => 'Tele2fon V3',
    15 => 'Tele2fon v5',
    16 => 'TELEFUNKEN Diamond TD1',
    17 => 'Telpad Dual S',
    18 => 'Telpad Quad S',
    19 => 'Telpad QS',
    20 => 'Tensent S9000',
    21 => 'Teracube 2e',
    22 => 'teXet X-alpha',
    23 => 'Texet TM-4083',
    24 => 'teXet X-start',
    25 => 'teXet iX-mini',
    26 => 'teXet X-navi',
    27 => 'TEXET TM-4677',
    28 => 'teXet X-medium plus',
    29 => 'teXet X-maxi qHD',
    30 => 'TeamDRH ICS for GTablet',
    31 => 'TERRA PAD 1004',
    32 => 'TERRA PAD 1005',
    33 => 'Telenor!!',
  ),
  '@TF' => 
  array (
    0 => 'TF101',
    1 => 'TF101-WiMAX',
    2 => 'TF201',
    3 => 'tf201t',
    4 => '(Transformer )?(Pad )?TF300(T|TG|TL)!',
    5 => '(Transformer )?(Pad )?TF502(T)!',
    6 => '(Transformer )?(Pad )?TF700(T|K)!',
  ),
  '@TG' => 
  array (
    0 => 'TG-L800S',
    1 => 'TG-L900S',
  ),
  '@TH' => 
  array (
    0 => 'thor',
    1 => 'Thunder S220',
    2 => '(Cloudfone|CloudPad|Excite|Thrill)!!',
    3 => '(KAZAM|Thunder|Tornado|Trooper)!!',
    4 => 'ThinkPad( Tablet)?!',
    5 => '(CJ-)?ThL!!',
    6 => 'THRiVE',
  ),
  '@TI' => 
  array (
    0 => 'Tilapia',
    1 => 'Titan TV S320',
    2 => 'TIQ-1049',
    3 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    4 => '(Karbonn|Titanium)!!',
    5 => 'TI320-DU',
    6 => 'TI320-DU TV',
    7 => 'TizzBird!!',
    8 => 'Titan pocket',
    9 => 'tiffany',
  ),
  '@TL' => 
  array (
    0 => 'TLINK[0-9]{3,3}!!',
  ),
  '@TM' => 
  array (
    0 => 'TM400',
    1 => 'TM105',
    2 => 'TM105A',
    3 => 'TM-3200R',
    4 => 'TM-3204R',
    5 => 'TM-3500',
    6 => 'TM-4003',
    7 => 'TM-4004',
    8 => 'TM-4071',
    9 => 'TM-4082R/X-driver',
    10 => 'TM-4377',
    11 => 'TM-4515',
    12 => 'TM-4577',
    13 => 'TM-4677',
    14 => 'TM-4982',
    15 => 'TM-5005',
    16 => 'TM-5200',
    17 => 'TM-5377',
    18 => 'TM-7011',
    19 => 'TM-7016',
    20 => 'TM-7021',
    21 => 'TM-7023',
    22 => 'TM-7024',
    23 => 'TM-7025',
    24 => 'TM-7026',
    25 => 'TM-7026 (revision 4)',
    26 => 'TM-7037W',
    27 => 'TM-7038W',
    28 => 'TM-7041',
    29 => 'TM-7043XD',
    30 => 'TM-7047HD 3G',
    31 => 'TM-7096',
    32 => 'TM-7099',
    33 => 'TM-7854',
    34 => 'TM-8041HD',
    35 => 'TM-8048 revision1',
    36 => 'TM-8051',
    37 => 'TM-9720',
    38 => 'TM-9725',
    39 => 'TM-9740',
    40 => 'TM-9741',
    41 => 'TM-9743W',
    42 => 'TM-9747!',
    43 => 'TM-9748 3G',
    44 => 'TM-9750HD',
    45 => 'TM-9751HD',
    46 => 'TM-9757',
    47 => 'TM-9757 3G rev1',
    48 => 'TM-9767',
    49 => 'TM-1058',
    50 => 'tmn!!',
  ),
  '@TN' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@TO' => 
  array (
    0 => 'Toro',
    1 => 'Toro-VZW',
    2 => 'TOUCAN Stick HD',
    3 => 'TOUCAN Stick G4',
    4 => 'Toucan Stick 4K',
    5 => 'TOUCAN Stick 3D mk2',
    6 => 'TOUCAN Stick 3D Pro',
    7 => 'TOUCHBOOK7.0 3G',
    8 => '(HP )?Touchpad!',
    9 => 'Touch Pro 2',
    10 => '(KAZAM|Thunder|Tornado|Trooper)!!',
    11 => 'Torque',
    12 => 'Touchlet X10.dual',
    13 => 'tolino tab!!',
    14 => 'TomTom Bridge',
    15 => 'TomTom PRO 8275',
    16 => 'TOOKY!!',
    17 => 'TOSHIBA AC AND AZ',
    18 => 'TOSHIBA FOLIO AND A',
    19 => 'Tostab03',
    20 => 'TOSHIBA WT7-C',
    21 => 'TOSHIBA WT8-A',
  ),
  '@TP' => 
  array (
    0 => 'TPA60W',
    1 => 'TPC-7151',
  ),
  '@TQ' => 
  array (
    0 => 'TQ150',
  ),
  '@TR' => 
  array (
    0 => 'Transformer',
    1 => 'Transformer TF101(G)?!',
    2 => '(Eee Pad )?Transformer Prime TF201!',
    3 => 'Transformer TF201',
    4 => 'Transformer TF201G',
    5 => 'Transformer Prime',
    6 => 'Transformer Prime TF300T',
    7 => 'Transformer 300',
    8 => 'Transformer Pad',
    9 => '(Transformer )?(Pad )?TF300(T|TG|TL)!',
    10 => '(Transformer )?(Pad )?TF502(T)!',
    11 => 'Transformer Pad TF600T',
    12 => '(Transformer )?(Pad )?TF700(T|K)!',
    13 => 'Transformer (Pad )?Infinity!',
    14 => 'TR720F',
    15 => 'TREKKER-X3',
    16 => 'Trekker-X4',
    17 => 'TR10CS1',
    18 => 'TripNiCE Pyramid',
    19 => '(HW-|HUAWEI )?(ATU|DRA|DVC|FLA|JKM|TIT|TAG||MRD|NCE|POT|TRT|SLA)!!',
    20 => 'TR10RS1',
    21 => '(KAZAM|Thunder|Tornado|Trooper)!!',
    22 => 'Triumph',
    23 => 'TRACER OXYGEN GS1',
    24 => 'Trevi PHABLET 4C',
    25 => 'Trevi PHABLET 5 S',
    26 => 'TREQ!!',
    27 => 'Trevi REVERSE 5.5Q',
    28 => 'Trevi PHABLET 6 S',
    29 => 'TRUE BEYOND 3G',
  ),
  '@TS' => 
  array (
    0 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
    1 => 'TSP21',
    2 => 'TSB CLOUD COMPANION;TOSHIBA AC AND AZ',
  ),
  '@TT' => 
  array (
    0 => 'TT101',
  ),
  '@TU' => 
  array (
    0 => 'TURBO DG2014',
    1 => 'Turbo C4 Plus',
    2 => 'Turbo-X pi',
    3 => 'Turbo-X Ice',
    4 => 'Turbo-X Tablet Spice III',
    5 => 'Turkcell!!',
    6 => 'Turk Telekom TT175',
  ),
  '@TV' => 
  array (
    0 => 'TVE9603I',
    1 => 'TVPAD Slim K3409',
  ),
  '@TX' => 
  array (
    0 => 'TX201LA',
    1 => 'TX201LAF',
    2 => 'TX85',
    3 => 'TX18',
  ),
  '@U ' => 
  array (
    0 => 'U FEEL LITE',
    1 => 'U FEEL PRIME',
    2 => 'U PULSE LITE',
  ),
  '@U1' => 
  array (
    0 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
    1 => 'U1',
    2 => 'U1203',
  ),
  '@U2' => 
  array (
    0 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
    1 => 'U20(a|i|iv)!',
  ),
  '@U3' => 
  array (
    0 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
  ),
  '@U5' => 
  array (
    0 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
  ),
  '@U6' => 
  array (
    0 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
  ),
  '@U7' => 
  array (
    0 => 'U701',
    1 => 'U701T',
    2 => 'U702',
    3 => 'U705',
    4 => 'U705T',
    5 => 'U705W',
    6 => 'U707',
    7 => 'U707T',
    8 => 'U708',
    9 => 'U7011',
    10 => 'U7015',
  ),
  '@U8' => 
  array (
    0 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
    1 => 'U8[0-9]{3,3}!!',
    2 => 'U8220',
  ),
  '@U9' => 
  array (
    0 => '(CUBE ?)?(K8|U1|U2|U3|U5|U6|U8|U9)[0-9]?GT!!',
    1 => 'U9[0-9]{3,3}!!',
  ),
  '@UG' => 
  array (
    0 => 'ugglite',
  ),
  '@UL' => 
  array (
    0 => 'Ultra',
    1 => 'Ultra Flare v2',
    2 => 'Ultimix!!',
    3 => 'Ultra Air',
    4 => 'Ultra Charm',
    5 => 'Ultra Energy',
    6 => 'Ultra Energy Lite',
    7 => 'Ultra Energy Plus',
    8 => 'Ultra Latitude',
    9 => 'Ultra Style',
    10 => 'Ultra Wave',
    11 => 'Ultimate10',
    12 => 'Ultimate10-Android4.0',
    13 => 'Ultimate 10',
    14 => 'Ulefone Armor 5',
    15 => 'Ulefone Be Touch2',
    16 => 'ultrafone!!',
    17 => 'Ultym 5',
    18 => 'Ultym 5L',
    19 => 'Ultym 5.2',
  ),
  '@UM' => 
  array (
    0 => 'UM840',
    1 => 'UMI!!',
  ),
  '@UN' => 
  array (
    0 => 'unknown M200-L09',
    1 => 'UNO X8',
    2 => 'UNO X10',
    3 => 'Unusual!!',
  ),
  '@UO' => 
  array (
    0 => 'UOOGOU!!',
  ),
  '@UR' => 
  array (
    0 => 'URBANO PROGRESSO',
  ),
  '@US' => 
  array (
    0 => 'USCC ALCATEL one touch 909B',
    1 => 'USCC ALCATEL one touch 988',
    2 => '(HTC|PCD|USCC)?ADR[0-9]{4,4}!!',
    3 => 'USCCADR[0-9]{4,4}!!',
    4 => '(USCC-|KYOCERA-)?E[0-9]{4,4}!!',
    5 => '(USCC-|KYOCERA-)?C[0-9]{4,4}!!',
    6 => 'USCC-(LG)?US!!',
    7 => 'Usmart!!',
  ),
  '@UT' => 
  array (
    0 => 'UTime!!',
  ),
  '@V ' => 
  array (
    0 => '(ZTE ?)?V ?[0-9]{3,3}!!',
  ),
  '@V-' => 
  array (
    0 => 'V-T100',
  ),
  '@V1' => 
  array (
    0 => 'V1 Viper',
    1 => 'V1 Viper E',
    2 => 'V1 Viper I',
    3 => 'V1 Viper I4G',
    4 => 'V1 Viper I4G PL',
    5 => 'V1 Viper I4G TM',
    6 => 'V1 Viper L',
    7 => 'V1 ViperS',
    8 => 'V1 Viper S4G',
    9 => 'V1 Viper S4G TM',
    10 => 'V11',
    11 => 'V17HD',
    12 => 'V105A4-A-C1',
    13 => 'V100MDT',
    14 => '(GIO-)?(GiONEE[- ])?V182!',
    15 => '(GIO-)?(GiONEE[- ])?V185!',
    16 => '(GIO-)?(GiONEE[- ])?V188!',
    17 => 'V1',
    18 => 'V1277',
    19 => 'V1916A',
  ),
  '@V2' => 
  array (
    0 => 'V2 Viper',
    1 => 'V2 Viper I',
    2 => 'V2 Viper I TM',
    3 => 'V2 Viper I4G',
    4 => 'V2 Viper S',
    5 => 'V2 Viper X',
    6 => 'V2 Viper X plus',
    7 => 'V2023',
    8 => 'V2028',
    9 => 'V2041',
    10 => 'V2056A',
    11 => 'V2109',
    12 => 'V2141A',
  ),
  '@V3' => 
  array (
    0 => 'v360 E1',
  ),
  '@V5' => 
  array (
    0 => 'V5U-simvalley SP-142',
    1 => 'V55',
  ),
  '@V7' => 
  array (
    0 => 'V701s Core4',
    1 => 'V711s Core4',
    2 => 'V712 Core4',
    3 => 'V719 3G',
  ),
  '@V8' => 
  array (
    0 => 'V801 Core4',
    1 => 'V801s Core4',
    2 => 'V811 Core4',
    3 => 'V812 Core4',
    4 => 'V813 Core4',
    5 => 'V813s Core4',
    6 => 'V818',
    7 => 'V818mini',
    8 => 'V819mini',
    9 => 'V819 3G',
    10 => 'V819i',
    11 => 'V8000 USA Cricket',
    12 => 'V8110',
    13 => 'V8403',
    14 => 'V8405',
    15 => 'V8408',
    16 => 'V8409',
    17 => 'V8412',
    18 => 'V8413',
    19 => 'V8501',
    20 => 'V8502',
    21 => 'V8503',
    22 => 'V8505',
    23 => 'V8507',
    24 => 'V8508',
    25 => 'V8510',
    26 => 'V8511',
    27 => 'V8602',
    28 => 'V8603',
  ),
  '@V9' => 
  array (
    0 => 'V99',
    1 => 'V936',
    2 => 'V985',
    3 => 'V971 Core4',
    4 => 'V971s Core4',
    5 => 'V972 Core4',
    6 => 'V973 Core4',
    7 => 'V975 Core4',
    8 => 'V975i',
    9 => 'V975m',
    10 => 'V975m Core4',
    11 => 'V975s Core4',
    12 => 'V961',
    13 => 'V9',
    14 => 'V9S',
    15 => 'V9e',
    16 => 'V9e+',
    17 => 'V9A',
    18 => 'V9C',
  ),
  '@VA' => 
  array (
    0 => 'Vandroid S5E',
    1 => 'Vandroid T1J',
    2 => 'Vandroid T3-A',
    3 => 'Vandroid T3i',
    4 => 'Vandroid T',
    5 => 'VAP430',
  ),
  '@VE' => 
  array (
    0 => 'vexpress a9',
    1 => 'Ventana',
    2 => 'Vendor Optimus',
    3 => 'VEOLO',
    4 => 'VEOLO2',
    5 => 'Vega',
    6 => 'VegaBean!',
    7 => 'Venture',
    8 => 'Venue!!',
    9 => '(OV-|Overmax|Vertis)!!',
    10 => 'VEGA NO.6',
    11 => 'Verifone Carbon-8',
    12 => 'verykool!!',
    13 => 'Vertu Aster T',
    14 => 'Vertu Signature Touch',
    15 => 'VERTU Ti',
    16 => 'Venus_V3 5040',
    17 => 'Venus V3 5570',
    18 => 'Venus V3 5580',
    19 => 'Venus Z20',
  ),
  '@VF' => 
  array (
    0 => 'VF685',
    1 => 'VF695',
    2 => 'VF696',
    3 => 'VF-696',
    4 => 'VF-795',
    5 => 'VF-895N',
    6 => 'VF945',
    7 => 'VFD 100',
    8 => 'VFD 200',
    9 => 'VFD 300',
    10 => 'VFD 301',
    11 => 'VFD 311',
    12 => 'VFD 500',
    13 => 'VFD 501',
    14 => 'VFD 510',
    15 => 'VFD 511',
    16 => 'VFD 513',
    17 => 'VFD 600',
    18 => 'VFD 610',
    19 => 'VFD 700',
    20 => 'VFD 710',
    21 => 'VFD 900',
    22 => 'VF-1397',
    23 => 'VF-1497',
    24 => 'VFD 1100',
    25 => 'VFD 1300',
    26 => 'VFD 1400',
    27 => 'VFD1400',
  ),
  '@VI' => 
  array (
    0 => 'VirtualBox!',
    1 => 'victoriabay',
    2 => 'Viva C701',
    3 => 'Viva H701',
    4 => 'Viva H701 LTE CZ',
    5 => 'VivaH7LTE',
    6 => 'VivaH8LTE',
    7 => 'VivaH10LTE',
    8 => 'Viva i7G',
    9 => 'VIVA i7',
    10 => 'Viva i10HD',
    11 => 'Viva i701G TM',
    12 => '(BLU|DASH|LIFE|NEO|STUDIO|VIVO)!!',
    13 => 'VICTORY',
    14 => 'Victory 2',
    15 => 'Victory 3',
    16 => 'VICTORY 4',
    17 => 'Victory 5',
    18 => 'VIA F1',
    19 => 'VIA S10',
    20 => 'Vibo-A688',
    21 => 'VitMod ExtraLite 1.6.5.fullodex for HTC HD7 Pro',
    22 => 'Vivid 4G',
    23 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    24 => 'Vibe K5 Plus',
    25 => 'VIBE X2 (X2)',
    26 => 'VIBE X3',
    27 => 'VIBE Z2 Pro (K920)',
    28 => 'Vibrantmtd',
    29 => 'Vibrant T959',
    30 => 'victorys s8',
    31 => 'Victorys V8',
    32 => 'Videocon!!',
    33 => 'ViewSonic-V350',
    34 => 'ViewSonic V500',
    35 => 'ViewPhone3',
    36 => 'ViewPad 4',
    37 => 'ViewSonic-ViewPad4',
    38 => 'ViewPad7',
    39 => 'ViewPad 7D',
    40 => 'ViewPad 7D Pro',
    41 => 'ViewSonic-ViewPad7e',
    42 => 'ViewPad7e',
    43 => 'ViewPad 7Q',
    44 => 'ViewPad 7Q Plus',
    45 => 'ViewPad 7Q Pro',
    46 => 'ViewPad7X',
    47 => 'ViewPad 8E',
    48 => 'ViewPad 9Q',
    49 => 'ViewPad 10e',
    50 => 'ViewPad 10S',
    51 => 'ViewPad 70Q',
    52 => 'ViewPad97A',
    53 => 'ViewPad97a K1',
    54 => 'ViewPad97a Pro',
    55 => 'ViewPad 97Q',
    56 => 'ViewPad 100Q',
    57 => 'ViewPad 100N Pro',
    58 => 'ViewPad 701N',
    59 => 'ViewPad i7D',
    60 => 'ViewPad E100',
    61 => 'ViewSonic VB733',
    62 => 'VINUS!!',
    63 => '(BBG-|VIV-)?vivo!!',
    64 => 'View',
    65 => 'View XL',
    66 => 'View2 Go',
    67 => 'View2 Plus',
    68 => 'Viettel i5',
    69 => 'Viettel I8',
    70 => 'VIETTEL V8404!',
    71 => 'Viettel V8410',
    72 => 'VIETTEL V8411',
    73 => 'Viettel V8502',
    74 => 'VIETTEL V8506',
    75 => 'VIETTEL V8509',
  ),
  '@VK' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    1 => 'VK410',
    2 => 'VK700',
    3 => 'VK810 4G',
    4 => 'VK815',
  ),
  '@VL' => 
  array (
    0 => 'VL-[0-9]{3,3}!!',
  ),
  '@VM' => 
  array (
    0 => 'VMware Virtual Platform',
    1 => '(AC|BC|LC|MT|RC|QS|VM|TS|OC)[0-9]{4,4}[A-Z]!!',
    2 => 'VM670',
  ),
  '@VN' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
  ),
  '@VO' => 
  array (
    0 => 'Vogue',
    1 => 'Volantis',
    2 => 'Volantisg',
    3 => 'VOYAGER DG300',
    4 => 'VOYAGER2 DG310',
    5 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    6 => 'Vox',
    7 => 'Vortex',
    8 => 'vollo Vi86',
    9 => 'VOTO GT2++',
    10 => 'VOTO GT7',
    11 => 'VOTO GT11',
    12 => 'VOTO GT11 Pro',
    13 => 'VOTO GT18',
    14 => 'VOTO V5',
    15 => 'VOTO VT868',
    16 => 'VOTO VT888',
    17 => 'VOTO VT898',
    18 => 'VOTO VT898S',
    19 => 'VOTO W5300',
    20 => 'VOTO X2',
    21 => 'VOYO A15',
    22 => 'voyo X6',
    23 => 'Vodafone!!',
  ),
  '@VS' => 
  array (
    0 => 'VS[0-9]{3,3}!!',
    1 => 'VS TOUCH!!',
    2 => 'VSP250g',
    3 => 'VSP250s',
    4 => 'VSD220',
    5 => 'VSD221',
    6 => 'VSD224',
    7 => 'VSD231',
    8 => 'VSD241',
    9 => 'Vsun HEXA',
    10 => 'VSUN ILLUSION',
    11 => 'VSUN RACE',
    12 => 'Vsun SPARK',
    13 => 'VSUN TOUCH',
    14 => 'Vsun D3B',
    15 => 'Vsun H3',
    16 => 'Vsun H9',
    17 => 'Vsun i1',
    18 => 'Vsun i5',
    19 => 'Vsun i9',
    20 => 'Vsun V9',
  ),
  '@VT' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    1 => 'VT6078',
    2 => 'VT75C',
    3 => 'VT77C',
    4 => 'VT79C',
    5 => 'VT87C+',
    6 => 'VTAB1008',
    7 => 'VTAB3010',
  ),
  '@VW' => 
  array (
    0 => 'VW RCBKK1',
  ),
  '@VX' => 
  array (
    0 => 'VX-100',
    1 => 'VX580A',
    2 => 'VX580W',
    3 => 'VX610A',
  ),
  '@W ' => 
  array (
    0 => 'W K300',
    1 => 'W K400',
    2 => 'W P200',
    3 => 'W C800',
    4 => 'W C860',
  ),
  '@W-' => 
  array (
    0 => 'W-V600',
    1 => 'W-P311-EEA',
    2 => 'W-V800-EEA',
    3 => 'W-V830-EEA',
    4 => 'W-V730-EEA',
    5 => 'W-V851-EEA',
  ),
  '@W1' => 
  array (
    0 => 'W10',
    1 => 'W10 V2.0',
    2 => 'w13pro',
    3 => 'W13PRO V2.0',
    4 => 'W17PRO(Dualcore)',
    5 => 'W17PRO JB Dualcore',
    6 => 'W100',
  ),
  '@W2' => 
  array (
    0 => 'W200',
    1 => 'W22PRO',
    2 => 'W22PRO 3G',
    3 => 'W27(Dualcore)',
    4 => 'W28(Dualcore)',
    5 => 'W20',
  ),
  '@W3' => 
  array (
    0 => 'W337',
    1 => 'W3620',
    2 => 'W30(QuadCore)',
    3 => 'w30hd(QuadCore)',
    4 => 'W30HDPRO',
  ),
  '@W4' => 
  array (
    0 => 'w42(QuadCore)',
  ),
  '@W5' => 
  array (
    0 => 'W5510',
  ),
  '@W6' => 
  array (
    0 => 'W606',
    1 => 'W619',
    2 => 'W686',
    3 => 'W626',
    4 => 'W6350',
    5 => 'W6360',
    6 => 'W6500',
    7 => 'W6620',
    8 => 'W6',
    9 => 'W6HD ICS',
    10 => 'W6HD ICS FULL',
  ),
  '@W7' => 
  array (
    0 => 'W711',
    1 => 'W757',
    2 => 'W700',
    3 => 'W732',
  ),
  '@W8' => 
  array (
    0 => 'W820',
    1 => '(GIO-)?(GiONEE[- ])?W800!',
    2 => 'W800',
    3 => 'W806',
    4 => 'W808',
    5 => 'W810',
    6 => 'W880',
    7 => 'W8',
    8 => 'W8 beyond',
  ),
  '@W9' => 
  array (
    0 => 'W900',
    1 => 'W990',
  ),
  '@WA' => 
  array (
    0 => '(HW-|HUAWEI )?(ALE|ANA|ANE|CDY|CLT|D2|DAV|ELE|ELS|EML|EVA|FIG|G6|G7|GRA|JNY|MAR|M100|P2|P6|P7|PPA|PRA|RIO|SC|Sophia|STK|VIE|VKY|VNS|VOG|VTR|WAS)!!',
    1 => 'WA-U420D',
    2 => 'WALSUN S1 Pro',
    3 => 'WALSUN S2',
    4 => 'Walton Primo',
    5 => 'WALTON Primo C1',
    6 => 'WALTON Primo-D1',
    7 => 'WALTON Primo E8+',
    8 => 'Walton F2',
    9 => 'Walton Primo F2',
    10 => 'WALTON Primo-G1',
    11 => 'WALTON Primo G1',
    12 => 'Walton-GH2',
    13 => 'Walton Primo H1',
    14 => 'Walton Primo H3',
    15 => 'WALTON H3',
    16 => 'WALTON Primo HMmini',
    17 => 'WALTON Primo-N1',
    18 => 'Walton Primo R1',
    19 => 'Walton RM2',
    20 => 'WALTON Primo S3',
    21 => 'Walton Primo VX+',
    22 => '(WALTON )?Primo-X1!',
    23 => 'Walpad 8b',
    24 => 'Walpad 8x',
    25 => 'Walpad 10b',
    26 => 'Walpad 10x',
    27 => 'Walpad C',
    28 => 'Walpad G',
    29 => 'Walpad G2',
    30 => 'Walpad G2i',
    31 => 'Walpad M',
    32 => 'WAX',
  ),
  '@WE' => 
  array (
    0 => 'WellcoM-A86',
    1 => 'WellcoM-A88',
    2 => 'WellcoM-A89',
    3 => 'WellcoM-A89-Plus',
    4 => 'WellcoM-A99',
    5 => 'WellcoM-A800',
    6 => 'WEXLER. ZEN 5',
    7 => 'WEXLER TAB7ID',
    8 => 'WEXLER-TAB-7iS',
    9 => 'WEXLER-TAB-7T',
    10 => 'WEXLER.BOOK T7008',
  ),
  '@WF' => 
  array (
    0 => 'WF7C',
  ),
  '@WH' => 
  array (
    0 => 'WHAM D5',
    1 => 'WHAM Q4',
    2 => 'WHAM-WD38',
    3 => 'WHAM WG40',
    4 => 'WHAM WG50',
    5 => 'WHAM WK41',
    6 => 'Wham WK44',
    7 => 'WHAM WS35',
    8 => 'WHAM WS36',
    9 => 'WHAM WS43',
    10 => 'WHAMWHAM WS43 Maui',
    11 => 'WHAM WT72',
  ),
  '@WI' => 
  array (
    0 => 'Wingray',
    1 => 'Wildfire S!',
    2 => 'Wildfire!',
    3 => 'Wiko Cink Five',
    4 => 'Wiko Cink Peax',
    5 => 'WIKO-CINK PEAX',
    6 => 'Wiko Cink Slim',
    7 => 'WIKO-CINK SLIM',
    8 => 'Wiko Rainbow',
    9 => 'Wiko Stairway',
    10 => 'WIM Lite',
    11 => 'Wileyfox Spark',
    12 => 'Wileyfox Spark +',
    13 => 'Wileyfox Spark X',
    14 => 'Wileyfox Storm',
    15 => 'Wileyfox Swift',
    16 => 'Wink City S',
  ),
  '@WL' => 
  array (
    0 => 'WL-101GQC',
  ),
  '@WM' => 
  array (
    0 => 'WM8650',
    1 => 'WM8650-mid',
    2 => 'wm8710-tvbox',
    3 => 'WM8850-mid',
  ),
  '@WO' => 
  array (
    0 => 'Woxter!!',
  ),
  '@WS' => 
  array (
    0 => 'WS171',
  ),
  '@WT' => 
  array (
    0 => 'WT[0-9]{2,2}[a-z]!!',
  ),
  '@WX' => 
  array (
    0 => 'WX04K',
    1 => 'WX06K',
    2 => 'WX10K',
    3 => 'WX[0-9]{3,3}!!',
    4 => 'WX04SH',
    5 => 'WX05SH',
    6 => 'Wxt Tab PC 65CXi',
  ),
  '@X-' => 
  array (
    0 => '(Explay|X-tremer|ActiveD|Informer|Surfer)!!',
    1 => 'X-treme PQ11',
    2 => 'X-treme PQ15',
    3 => 'X-tremePQ22',
    4 => 'X-treme-PQ30',
    5 => 'X-basic',
    6 => 'X-basic2',
    7 => 'X-maxi',
    8 => 'X-medium',
  ),
  '@X0' => 
  array (
    0 => 'X007D',
    1 => 'X008D',
    2 => 'X008DA',
    3 => 'X008DB',
    4 => 'X008DC',
    5 => 'X009D',
    6 => 'X009DA',
    7 => 'X009DB',
    8 => 'X009DD',
    9 => 'X00AD',
    10 => 'X00ADA',
    11 => 'X00ADC',
    12 => 'X00BD',
    13 => 'X00DD',
    14 => 'X00DDA',
    15 => 'X00DDB',
    16 => 'X00HD',
    17 => 'X00GD',
    18 => 'X00LD',
    19 => 'X00T',
    20 => 'X00TD',
    21 => 'X00TDB',
    22 => 'X00DE',
    23 => 'X00QD',
    24 => 'X00QSA',
    25 => 'X01AD',
    26 => 'X01BDA',
    27 => 'X013D',
    28 => 'X013DA',
    29 => 'X013DB',
    30 => 'X013DC',
    31 => 'X015D',
    32 => 'X00ID',
    33 => 'X014D',
    34 => 'X018D',
    35 => 'X002',
    36 => 'X003',
    37 => 'X005',
    38 => 'X008',
  ),
  '@X1' => 
  array (
    0 => 'X1 Soul',
    1 => 'X1 Soul Mini',
    2 => 'X1 mini Soul',
    3 => 'X1 Soul Xtreme',
    4 => 'X1 Xtreme Mini',
    5 => 'X1',
    6 => 'X1 7.0',
    7 => 'X1 atom',
    8 => 'X1 atom L',
    9 => 'X1 Atom s',
    10 => 'X1 Beats',
    11 => 'X1 Grand',
    12 => 'X1 mini',
    13 => 'X1 Selfie',
    14 => 'X1010',
    15 => 'X1030X',
    16 => 'X1031X',
    17 => 'X1060X',
    18 => 'X10(a|i|iv|i-o|s)?$!',
    19 => 'X10 ?(Mini ?Pro)$!',
    20 => 'X10 ?(Mini)$!',
    21 => 'X10H(G3C5)',
    22 => 'X10HD 3G(M6K6)',
    23 => 'X16 Plus(D3A5)',
    24 => 'X16 Power(D6F7)',
    25 => 'X16 PRO(D6F8)',
    26 => 'X16HD (K3J3)',
    27 => 'X16HD 3G(M5J4)',
    28 => 'X16HD 3G(M5J5)',
    29 => 'X16HD 3G(M5J5',
    30 => 'X16HD 3G(M5J6)',
  ),
  '@X2' => 
  array (
    0 => 'X2 Soul',
    1 => 'X2 Soul Lite',
    2 => 'X2 Soul Lite TM',
    3 => 'X2 Soul Mini',
    4 => 'X2 Soul Mini TM',
    5 => 'X2 Soul PRO',
    6 => 'X2 Soul Style',
    7 => 'X2 Soul Style TM',
    8 => 'X2 Soul Style Plus',
    9 => 'X2 Soul Xtreme',
    10 => 'X2 Twin',
  ),
  '@X3' => 
  array (
    0 => 'X3 Soul',
    1 => 'X3 Soul Lite',
    2 => 'X3 Soul Lite TM',
    3 => 'X3 Soul mini',
    4 => 'X3 Soul PLUS',
    5 => 'X3 Soul PRO',
    6 => 'X3 Soul Style',
    7 => 'X325a',
    8 => 'X3-Ice MIUI XT720 Memorila Classics',
  ),
  '@X4' => 
  array (
    0 => 'X403',
  ),
  '@X5' => 
  array (
    0 => 'X5 Soul Pro',
    1 => 'X550',
    2 => 'X515d',
    3 => 'X515e',
    4 => 'X525a',
    5 => 'X527',
    6 => 'X505',
    7 => 'X506',
    8 => 'X506S',
    9 => 'X507Q',
  ),
  '@X6' => 
  array (
    0 => 'X67 5G',
  ),
  '@X7' => 
  array (
    0 => 'X710d',
    1 => 'X720D',
    2 => 'X710E',
    3 => 'X7G',
    4 => 'X720',
    5 => 'X70(C6F9)',
    6 => 'X70 3G (C5D9)',
    7 => 'X70 R(C7F9)',
    8 => 'X70 R(C8F9)',
    9 => 'X708S',
  ),
  '@X8' => 
  array (
    0 => '(GIO-)?(GiONEE[- ])?X817!',
    1 => 'X800+',
    2 => 'X8',
    3 => 'X80(C4M5)',
    4 => 'X80h',
    5 => 'X80h(FB5M)',
    6 => 'X80h(FB6M)',
    7 => 'X80HD',
    8 => 'X80HD(G2N3)',
    9 => 'X80HD(G2N4)',
    10 => 'X80HD(G2N5)',
    11 => 'X80HD(G2N6)',
    12 => 'X80HD(G2N8)',
    13 => 'X80HD(G2N9)',
    14 => 'X80HD(G2N9',
    15 => 'X80 Plus(H5C5)',
    16 => 'X80 Plus(H6C3)',
    17 => 'X80 Plus(H6C3',
    18 => 'X80 Plus(H6C4)',
    19 => 'X80 Power(B2N3)',
    20 => 'X80 Power(B2N4)',
    21 => 'X80 Power(B2N6)',
    22 => 'X80 Pro(E2E9)',
    23 => 'X80 Pro(E3E6)',
    24 => 'X80 Pro(E3E7)',
    25 => 'X80 Pro(E3E8)',
    26 => 'X80 Pro (E3E9)',
    27 => 'X81-8G3D',
    28 => 'X89 (E7ED)',
    29 => 'X89 (KR89)',
    30 => 'X89HD (H21C)',
  ),
  '@X9' => 
  array (
    0 => 'X920e',
    1 => 'X9',
    2 => 'X903',
    3 => 'X903S',
    4 => 'X905',
    5 => 'X906',
    6 => 'X907',
    7 => 'X909',
    8 => 'X909T',
    9 => 'X9000',
    10 => 'X9006',
    11 => 'X9007',
    12 => 'X9009',
    13 => 'X9015',
    14 => 'X9017',
    15 => 'X9070',
    16 => 'X9076',
    17 => 'X9077',
    18 => 'X9079',
    19 => 'X90HD (M2PC)',
    20 => 'X98 3G(HKC1)',
    21 => 'X98 3G(HKC2)',
    22 => 'X98 3G(HKC3',
    23 => 'X98 3G(HKC3)',
    24 => 'X98 3G(lite)',
    25 => 'X98 Air(C5KN)',
    26 => 'X98 Air Smile',
    27 => 'X98 Air 3G',
    28 => 'X98 Air 3G(Smile)',
    29 => 'X98 Air 3G(C5J5)',
    30 => 'X98 Air 3G(C5J6)',
    31 => 'X98 Air 3G(C5J8)',
    32 => 'X98 Air 3G(C6J6)',
    33 => 'X98 Air 3G(C6J6',
    34 => 'X98 Air 3G(C8J6)',
    35 => 'X98 Air 3G(C8J7)',
    36 => 'X98 Air 3G(C9J6)',
    37 => 'X98 Air 3G(C9J7)',
    38 => 'X98 Air 3G(C9J8)',
    39 => 'X98 Air II',
    40 => 'X98 Air II(HG5N)',
    41 => 'X98 Air II(HG6M)',
    42 => 'X98 Air II(HG7N)',
    43 => 'X98 Air II(HG8N)',
    44 => 'X98 Air II(HG9M)',
    45 => 'X98 Air II(HG9N)',
    46 => 'X98 Air II(HG9N',
    47 => 'X98 Air ? HG9M',
    48 => 'X98 Air Ⅱ HG9M',
    49 => 'X98 Air II(Smile)',
    50 => 'X98 Air III',
    51 => 'X98 Air III(M5C5)',
    52 => 'X98 Air III(M5C5',
    53 => 'X98 Air III(M5C6)',
    54 => 'X98 Plus',
    55 => 'X98 Plus(A6C7)',
    56 => 'X98 Plus(A5C8)',
    57 => 'X98 Plus 3G(A6C9)',
    58 => 'X98 Plus II (C2D4)',
    59 => 'X98 Plus II (C2D6)',
    60 => 'X98 Plus II (C2D8)',
    61 => 'X98 Plus II (C2E3)',
    62 => 'X98 Pro',
    63 => 'X98 Pro (K9C6)',
    64 => 'X98 Pro (K9C6',
    65 => 'X9180',
  ),
  '@XA' => 
  array (
    0 => 'Xamarin Android Player!',
  ),
  '@XC' => 
  array (
    0 => 'XCD 28',
    1 => 'XCD35',
  ),
  '@XD' => 
  array (
    0 => '(NMP|MBR|XDK|XDS|XMP)\\-!!',
    1 => 'XDP-100R',
  ),
  '@XE' => 
  array (
    0 => 'XELIO',
    1 => 'XELIO7PHONETAB',
    2 => 'Xelio 7 pro',
    3 => 'XELIO7PRO',
    4 => 'XELIO10 QUAD',
    5 => 'XELIO10 PLUS 3G',
    6 => 'XELIO10EXTREME',
    7 => 'Xelio 10 Pro',
    8 => 'Xelio10Pro',
    9 => 'XELIOPT2',
    10 => 'XelioPT2Pro',
    11 => 'Xelio PT4 Pro',
    12 => 'XELIO P900A',
    13 => 'Xenta-TAB07-210',
    14 => 'Xenta-TAB07-211',
    15 => 'Xenta TAB07-200',
    16 => 'Xenta TAB08-200',
    17 => 'Xenta TAB08-201-3G',
    18 => 'Xenta TAB9-200',
    19 => 'Xenta TAB09-211',
    20 => 'Xenta TAB10-211',
    21 => 'Xenta TAB10-201',
    22 => 'Xenta TAB13-201',
  ),
  '@XI' => 
  array (
    0 => 'Xiaomi',
    1 => '(Xiaomi )?(Xiaomi|Xiaomi M|MI)!!',
    2 => '(Xiaomi|Xiaomi Mi|MI) Note!!',
    3 => '(Xiaomi )?(MI )?MAX$!',
    4 => '(Xiaomi )?(MI )?MAX 2$!',
    5 => '(Xiaomi )?(MI )?MAX 3$!',
    6 => '(Xiaomi )?(MI )?MIX$!',
    7 => '(Xiaomi )?(MI )?MIX 2$!',
    8 => '(Xiaomi )?(MI )?MIX 2S$!',
    9 => '(Xiaomi )?20!!',
    10 => '(Xiaomi )?(Redmi|RedRice|HM)!!',
    11 => '(Xiaomi )?(Redmi|HM)[ \\-]?Note!!',
    12 => '(Xiaomi |HM)?20!!',
    13 => '(Xiaomi |HM)?21!!',
    14 => 'Xiaomi MDT2!',
    15 => 'Xiaomi MCT1!',
    16 => 'Xiaomi MAT136!',
    17 => 'Xiaomi MBT6A5!',
    18 => 'Xiaomi 11 Lite 5G NE',
    19 => 'Xiaomi 11T Pro',
    20 => '(Xiaomi|Xiaomi Mi|MI) Pad!!',
    21 => '(Xiaomi|Xiaomi Mi|MI)Box!!',
    22 => '(Xiaomi|Xiaomi Mi|MI)TV!!',
  ),
  '@XL' => 
  array (
    0 => 'XL39h',
    1 => 'Xlife-348E+',
    2 => 'Xlife-350',
    3 => 'Xlife-364 3G+',
    4 => 'Xlife-405',
    5 => 'Xlife-410 3G',
    6 => 'Xlife-415',
    7 => 'Xlife-431Q',
    8 => 'Xlife-431Q Lite',
    9 => 'Xlife-480q',
    10 => 'Xlife-481q',
    11 => 'Xlife-482q',
    12 => 'Xlife-514Q',
    13 => 'Xlife-515Q',
    14 => 'Xlife-M5q+',
    15 => 'Xlife-M44Q',
    16 => 'Xlife-Electro55HD',
    17 => 'Xlife-Ezy',
    18 => 'Xlife-Victor4',
  ),
  '@XM' => 
  array (
    0 => '(NMP|MBR|XDK|XDS|XMP)\\-!!',
    1 => 'XM50h',
    2 => 'XM50t',
  ),
  '@XO' => 
  array (
    0 => 'XOOM',
    1 => 'XOOM 2!',
    2 => 'XOOM MZ606',
    3 => 'Xoom Wifi',
    4 => 'Xoom LTE',
    5 => 'Xoom 3G',
    6 => 'XO Learning tablet',
    7 => '(XOLO )?[ABQX][0-9]{3,4}!!',
    8 => 'XOLO One',
    9 => 'XOLO One16',
    10 => 'XOLO One HD',
    11 => 'XOLO One LFC',
    12 => 'XOLO Play',
    13 => 'XOLO PLAY T1000',
    14 => 'XOLO T1000',
    15 => 'Xolo QC800',
  ),
  '@XP' => 
  array (
    0 => 'Xperia Z Ultra',
    1 => 'XPRESS PRO',
    2 => 'XP8800',
    3 => 'Xperia!!',
    4 => '(Symphony|Xplorer)!!',
    5 => 'xPAD-70',
  ),
  '@XQ' => 
  array (
    0 => 'XQ-AT51',
    1 => 'XQ-AT52',
    2 => 'XQ-BC52',
    3 => 'XQ-BC72',
    4 => 'XQ-AS52',
    5 => 'XQ-BQ52',
    6 => 'XQ-AU52',
    7 => 'XQ-BT52',
    8 => 'XQ-AD51',
    9 => 'XQ-AD52',
    10 => 'XQ-BE52',
  ),
  '@XT' => 
  array (
    0 => 'Xtreme X2',
    1 => 'XT751',
    2 => 'XT[0-9]{3,3}!!',
    3 => 'xt880b',
    4 => '(Hisense )?(LED[0-9]{2,2}(G|K|L|EC|XT)[0-9]{2,3})!',
    5 => 'Xteam Smartpad 810c',
    6 => 'Xteam 4.8 Smartpad 810c',
    7 => 'XT[0-9]{3,4}!!',
    8 => 'Xtab1081HD',
    9 => 'Xtreme V10',
    10 => 'Xtreme V10i',
    11 => 'Xtreme V12',
    12 => 'Xtreme V15',
    13 => 'Xtreme V20',
    14 => 'Xtreme V21',
    15 => 'Xtreme V22',
    16 => 'Xtreme V25',
    17 => 'Xtreme-V30',
    18 => 'Xtreme V40i',
    19 => 'Xtreme V44',
    20 => 'xTAB-7X',
    21 => 'xTAB-9',
    22 => 'xTAB-70!',
    23 => 'xTAB-100!',
    24 => 'Xtouch X405',
    25 => 'Xtreamer Mobile AiKi',
    26 => 'Xtreamer Mobile AiKi a7*',
    27 => 'Xtreamer Mobile Aiki5s',
    28 => 'Xtreamer Joyz',
    29 => 'Xtreamer Wonder',
  ),
  '@XW' => 
  array (
    0 => 'XW-I8',
  ),
  '@Y2' => 
  array (
    0 => 'Y2',
  ),
  '@Y5' => 
  array (
    0 => 'Y538',
  ),
  '@Y6' => 
  array (
    0 => 'Y6 Max',
    1 => 'Y6 Piano',
  ),
  '@YA' => 
  array (
    0 => '(HW-|HUAWEI |HONOR )?(ATH|AUM|BLN|BKL|BKK|BND|CHE|CHM|COL|COR|DUA|DUK|DLI|EDI|FRD|HLK|HN3|H30|H60|HOL|HRY|JAT|JMM|JSN|LRA|KIW|KSA|LLD|NEM|NMO|NTH|NTN|OXF|PE|PCT|PLK|RNE|SCL|TNY|KNT|CAM|STF|HDN|YAL)!!',
  ),
  '@YD' => 
  array (
    0 => 'YD201',
    1 => 'YD202',
    2 => 'YD203',
    3 => 'YD206',
  ),
  '@YE' => 
  array (
    0 => 'Yellowstone',
    1 => '(Andy|Yezz)!!',
  ),
  '@YO' => 
  array (
    0 => 'youwave custom',
    1 => 'YOGA Tablet!!',
  ),
  '@YP' => 
  array (
    0 => 'YPY-72SIM',
    1 => 'YPY-73G',
    2 => 'Ypy 7 - TB07FTA',
    3 => 'YPY 07FTA',
    4 => 'YPY 07FTAB',
    5 => 'YPY 07FTAB PA',
    6 => 'YPY 07FTB',
    7 => 'YPY 07FTBF',
    8 => 'Ypy 7 - TB07STA',
    9 => 'YPY 07STB',
    10 => 'YPY 07STBF',
    11 => 'YPY 10FTA',
    12 => 'YPY10FTA',
    13 => 'YPY 10FTB',
    14 => 'YPY 10FTBF',
    15 => 'YPY 10STB',
    16 => 'YPY 10STBF',
    17 => 'YPY AB7D',
    18 => 'YPY AB7DC',
    19 => 'YPY AB10D',
    20 => 'YPY AB10DC',
    21 => 'YPY ABXD',
    22 => 'YPY J213',
    23 => 'YPY S350',
    24 => 'YPY S350 PLUS',
    25 => 'YPY S400',
    26 => 'YPY S405',
    27 => 'YPY S450',
    28 => 'YPY S460',
    29 => 'YPY S500',
    30 => 'YPY TQ7',
    31 => 'YP-G!!',
    32 => 'YP712',
  ),
  '@YQ' => 
  array (
    0 => 'YQ601',
  ),
  '@YU' => 
  array (
    0 => 'yukkabeach',
    1 => 'YU4711',
    2 => 'YU5010',
    3 => 'YU5010A',
    4 => 'YU5011',
    5 => 'YU5040',
    6 => 'YU5050',
    7 => 'YUREKA',
    8 => 'YU5200',
    9 => 'YUreka+',
    10 => 'YU5510',
    11 => 'YU5510A',
    12 => 'YU5530',
    13 => 'YU5551',
    14 => 'YU 6000',
    15 => 'YUSUN A7',
    16 => 'YUSUN A8',
    17 => 'YUSUN E98',
    18 => 'YUSUN L29',
    19 => 'YUSUN L63',
    20 => 'YUSUN L71',
    21 => 'YUSUN L88',
    22 => 'YUSUN LA2-T',
    23 => 'YUSUN LA2 T',
    24 => 'YUSUN LA2-T1',
    25 => 'YUSUN LA2 T1',
    26 => 'YUSUN LA2-W',
    27 => 'YUSUN LA2 W1',
    28 => 'YUSUN LA5-W',
    29 => 'YUSUN T22',
    30 => 'YUSUN-T22',
    31 => 'YUSUN T29',
    32 => 'YUSUN T30',
    33 => 'YUSUN T31',
    34 => 'YUSUN T35!',
    35 => 'YUSUN T50',
    36 => 'YUSUN T85',
    37 => 'YUSUN T808',
    38 => 'YUSUN W35',
    39 => 'YUSUN W90',
    40 => 'YUSUN W91',
    41 => 'YUSUN W306',
    42 => 'yusun W702',
    43 => 'YUSUN W706',
    44 => 'YUSUN W708',
    45 => 'YUSUN - W 800',
    46 => 'YUSUN--W 800',
    47 => 'YUSUN W808',
    48 => 'YUSUN -- W 900',
  ),
  '@YX' => 
  array (
    0 => 'YX-YUSUN E80',
    1 => 'YX-YUSUN E89',
    2 => 'YX-YUSUN E96',
  ),
  '@Z0' => 
  array (
    0 => 'Z002',
    1 => 'Z007',
    2 => 'Z008',
    3 => 'Z008D',
    4 => 'Z00A',
    5 => 'Z00AD',
    6 => 'Z00ADA',
    7 => 'Z00ADB',
    8 => 'Z00AS',
    9 => 'Z00D',
    10 => 'Z00ED',
    11 => 'Z00EDB',
    12 => 'Z00LD',
    13 => 'Z00LDC',
    14 => 'Z00LDD',
    15 => 'Z00MD',
    16 => 'Z00RD',
    17 => 'Z00TD',
    18 => 'Z00TDA',
    19 => 'Z00WD',
    20 => 'Z011D',
    21 => 'Z011DD',
    22 => 'Z00UD',
    23 => 'Z00UDA',
    24 => 'Z00UDB',
    25 => 'Z00UDC',
    26 => 'Z00UDH',
    27 => 'Z00SD',
    28 => 'Z00VD',
    29 => 'Z00XS',
    30 => 'Z00XSA',
    31 => 'Z00XSB',
    32 => 'Z010D',
    33 => 'Z010DA',
    34 => 'Z010DB',
    35 => 'Z010DD',
    36 => 'Z012D',
    37 => 'Z012DA',
    38 => 'Z012DB',
    39 => 'Z012DC',
    40 => 'Z012DE',
    41 => 'Z012S',
    42 => 'Z016D',
    43 => 'Z016S',
    44 => 'Z017D',
    45 => 'Z017DA',
    46 => 'Z017DB',
    47 => 'Z017DC',
    48 => 'Z01BD',
    49 => 'Z01BDA',
    50 => 'Z01BDB',
    51 => 'Z01BDC',
    52 => 'Z01BS',
    53 => 'Z01FD',
    54 => 'Z01GD',
    55 => 'Z01HD',
    56 => 'Z01HDA',
    57 => 'Z01KD',
    58 => 'Z01KDA',
    59 => 'Z01MD',
    60 => 'Z01MDA',
    61 => 'Z01RD',
    62 => 'Z01QD',
    63 => 'Z00YD',
  ),
  '@Z1' => 
  array (
    0 => '(BB )?Z10$!',
    1 => 'Z1-H39LW',
    2 => '(HUAWEI )?(ALP|BLA|CRR|EVR|HMA|LIO|LON|LYA|MATE|MHA|MT1|MT2|MT7|M200|NOH|NXT|SNE|TAS|Z100)!!',
    3 => 'Z1i',
  ),
  '@Z2' => 
  array (
    0 => 'Z2 Plus',
    1 => 'Z2 Rio',
    2 => 'Z282 C91',
  ),
  '@Z3' => 
  array (
    0 => '(BB )?Z30$!',
    1 => 'Z30Aire',
    2 => 'Z30Dart',
    3 => 'Z30Lite',
    4 => 'Z30PACE',
  ),
  '@Z4' => 
  array (
    0 => 'Z4',
    1 => 'Z40Lite+',
    2 => 'Z40Pro',
    3 => 'Z4OPro',
    4 => 'Z40ProLite',
    5 => 'Z4OProLite',
    6 => 'Z40QStar',
    7 => 'Z41 AIRE',
    8 => 'Z41Aire',
    9 => 'Z41Lite+',
    10 => 'Z42 Nova',
    11 => 'Z45 Amaze',
    12 => 'Z45 Dazzle',
    13 => 'Z45 Nova',
    14 => 'Z45 Nova+',
    15 => 'Z45 Quad',
    16 => 'Z45Q Star',
    17 => 'Z45Q Star+',
    18 => 'Z4 mini',
  ),
  '@Z5' => 
  array (
    0 => 'Z520e',
    1 => 'Z520m',
    2 => 'Z50 Nova',
    3 => 'Z50Pro',
    4 => 'Z50 Quad',
    5 => 'Z50Q Lite',
    6 => 'Z50Q Star',
    7 => 'Z51 blaze',
    8 => 'Z51 Nova',
    9 => 'Z51 Nova+',
    10 => 'Z51 PUNCH',
    11 => 'Z51 Quad',
    12 => 'Z51Q Star',
    13 => 'Z52 Inspire',
    14 => 'Z52 Thunder',
    15 => 'Z52 Thunder+',
  ),
  '@Z7' => 
  array (
    0 => 'Z71',
    1 => 'Z710',
    2 => 'Z710e',
    3 => 'Z715e',
  ),
  '@ZA' => 
  array (
    0 => 'Zaffire 785',
    1 => 'Zaffire 970',
    2 => 'ZA400',
    3 => 'ZA402',
    4 => 'ZA450',
    5 => 'ZA451',
    6 => 'ZA459',
    7 => 'ZA500',
    8 => 'ZA501',
    9 => 'ZA509',
    10 => 'ZA705',
    11 => 'ZA935',
    12 => 'ZA940',
    13 => 'ZA945',
    14 => 'ZA950',
    15 => 'ZA955',
    16 => 'ZA990',
    17 => 'ZA966',
    18 => 'ZA977',
    19 => 'ZA985',
    20 => 'ZA987',
  ),
  '@ZB' => 
  array (
    0 => 'ZB551KL',
    1 => 'ZB500KG',
    2 => 'ZB500KL',
    3 => 'ZB501KL',
    4 => 'ZBOX-ID18',
    5 => 'ZBOX-ID81',
  ),
  '@ZC' => 
  array (
    0 => 'ZC551KL',
    1 => 'ZC553KL',
    2 => 'ZC554KL',
  ),
  '@ZE' => 
  array (
    0 => 'ZenWatch',
    1 => 'ZenWatch 2',
    2 => 'Zenith',
    3 => 'Zenfone 5 LTE',
    4 => 'ZenFone 2',
    5 => 'ZenFone 2E',
    6 => 'ZE520KL',
    7 => 'ZE550KL',
    8 => 'ZE552KL',
    9 => 'ZE553KL',
    10 => 'ZE554KL',
    11 => 'ZEN Touch 2',
    12 => '(Highscreen|Alpha|Bay|Boost|Cosmo|Explosion|Power|Prime|Zera)!!',
    13 => 'ZEN 4.5',
    14 => 'ZEN 4.7',
    15 => 'ZEN 5+',
    16 => 'ZENITHINK C94!',
    17 => 'Zen PO neo',
    18 => 'ZEN U1',
    19 => 'ZEN U4',
    20 => 'ZEN U5',
    21 => 'ZEN 303 3G',
  ),
  '@ZI' => 
  array (
    0 => 'Zii!!',
    1 => 'Zio P2',
    2 => 'Zio',
    3 => 'ZiiLABS ViewBook 730',
    4 => 'ZIGO EON5I',
    5 => 'Zigo Eon6i',
    6 => 'Zigo Eon7i',
    7 => 'Zigo Eon52i',
    8 => 'Zigo Eon 53',
    9 => 'Zigo Nebula6 9',
    10 => 'Zigo Nebula Tab 7.1',
    11 => 'Zigo N81',
    12 => 'Ziss Ranger HD',
    13 => 'Zilo',
  ),
  '@ZO' => 
  array (
    0 => 'Zoom',
    1 => '(Zopo )?ZP ?[0-9]{3,3}!!',
  ),
  '@ZP' => 
  array (
    0 => '(Zopo )?ZP ?[0-9]{3,3}!!',
  ),
  '@ZS' => 
  array (
    0 => 'ZS671KS',
  ),
  '@ZT' => 
  array (
    0 => 'zt180',
    1 => 'ZTE A[0-9]{3,3}!!',
    2 => 'ZTE B[0-9]{3,3}!!',
    3 => 'ZTE BA[0-9]{3,3}!!',
    4 => 'ZTE BV0[0-9]{3,3}!!',
    5 => 'ZTE C[0-9]{3,3}!!',
    6 => 'ZTE C N[0-9]{3,3}!!',
    7 => 'ZTE C R[0-9]{3,3}!!',
    8 => 'ZTE C X[0-9]{3,3}!!',
    9 => 'ZTE ?G[0-9]{3,3}!!',
    10 => '(ZTE ?)?N[0-9]{3,3}!!',
    11 => '(ZXY-)?(ZTE )?N[0-9]{4,4}!!',
    12 => 'ZTE K813',
    13 => 'ZTE M[0-9]{3,3}!!',
    14 => 'ZTE P[0-9]{3,3}!!',
    15 => 'ZTE ?Q[0-9]{3,3}!!',
    16 => 'ZTE Q[0-9]!!',
    17 => 'ZTE R[0-9]{3,3}!!',
    18 => 'ZTE S[0-9]{3,3}!!',
    19 => 'ZTE T[0-9]!!',
    20 => 'ZTE T ?U[0-9]{3,3}!!',
    21 => '(ZTE ?)?U[0-9]{3,3}!!',
    22 => 'ZTE U N[0-9]{3,3}!!',
    23 => 'ZTE U[ \\(\\-]V[\\)\\-]?[0-9]{3,3}!!',
    24 => 'ZTE U X[0-9]{3,3}!!',
    25 => '(ZTE ?)?V[0-9]{3,3}[A-Z]!!',
    26 => '(ZTE ?)?V ?[0-9]{3,3}!!',
    27 => '(ZTE ?)?X[0-9]{3,3}!!',
    28 => '(ZTE )?Z[0-9]!!',
    29 => '(ZTE )?Blade!!',
    30 => 'ZTE Geek!!',
    31 => '(ZTE )?(Grand|Mimosa)!!',
    32 => '(ZTE )?Kis!!',
    33 => '(ZTE )?Racer!!',
    34 => '(ZTE )?Skate!!',
    35 => 'ZTE Libra',
    36 => 'ZTE LINK',
    37 => 'ZTE T T9',
    38 => 'ZTE V7073',
    39 => 'ZTE V9',
    40 => 'ZTE V9A',
    41 => 'ZTE C V9E',
    42 => 'ZTE V7273',
    43 => 'ZTE e-Learning PAD E8Q',
    44 => 'ZTE E10T',
    45 => 'ZTE E10Q',
    46 => 'ZTE R22',
    47 => 'ZTE R83',
    48 => 'ZTE R84',
    49 => 'ZTE V10',
    50 => 'ZTE V70',
    51 => 'ZTE V72',
    52 => 'ZTE V72A',
    53 => 'ZTE K88',
    54 => 'ZTE K97',
    55 => 'ZTE T T98',
    56 => 'ZTE TT98',
    57 => 'ZTE Crescent',
    58 => 'ZTE JOE',
    59 => 'ZTE 975',
    60 => 'ZTE Tureis',
    61 => 'ZTE CLARO Q1',
    62 => 'ZTE LEO M1',
    63 => 'ZTE LeoM1',
    64 => 'ZTE LEO S1',
    65 => 'ZTE LEO S2',
    66 => 'ZTE LEO Q1',
    67 => 'ZTE LEO Q2',
    68 => 'ZTE GV821',
    69 => 'ZTE-860U',
    70 => 'ZTE Roamer',
    71 => 'ZTE V9800',
    72 => 'ZTE U9810',
    73 => 'ZTE U9815',
    74 => 'ZTE V9815',
    75 => 'ZTE Grand Memo LTE',
    76 => 'ZTE V9820',
    77 => 'ZTE Nubia Z7',
    78 => 'ZTE Nubia X6',
    79 => 'ZTE Switch X1',
    80 => 'ZTE Switch X2',
    81 => 'ZTE Maxx',
    82 => 'ZTE Fit 4G Smart',
    83 => 'ZTE N5',
    84 => 'ZTE N5L',
    85 => 'ZTE N5S',
    86 => 'ZTE U5',
    87 => 'ZTE U5S',
    88 => 'ZTEU5S',
    89 => 'ZTE V5S',
    90 => 'ZTEV5S',
    91 => 'ZTE A2015',
    92 => 'ZTE A2016',
    93 => 'ZTE Axon 7',
    94 => 'ZTE A2017',
    95 => 'ZTE A2017G',
    96 => 'ZTE A2017U',
    97 => 'ZTE A2020G Pro',
    98 => 'ZTE A2022PG',
    99 => 'ZTE B2015',
    100 => 'ZTE B2016',
    101 => 'ZTE B2017',
    102 => 'ZTE B2017G',
    103 => 'ZTE B2019G',
    104 => 'ZTE C2016',
    105 => 'ZTE C2017',
    106 => 'ZTE STAR',
    107 => 'ZTE Star 1',
    108 => 'ZTE S2004',
    109 => 'ZTE S2005',
    110 => 'ZTE S2007',
    111 => 'ZTE S2010',
    112 => 'ZTE S2014',
    113 => 'ZTE U9180',
    114 => 'ZTE U9370',
    115 => 'ZTE V6500',
    116 => 'ZTE V8110',
    117 => 'ZTE B860A',
    118 => 'ZTE B860AV1',
    119 => 'ZTE W1010',
  ),
  '@ZU' => 
  array (
    0 => 'ZUK Z1',
    1 => 'ZUK Z2',
    2 => 'ZUK Z2131',
    3 => 'ZUK Z2151',
    4 => 'ZUK Z2 Plus',
    5 => 'ZUK Z2132',
    6 => 'ZUK Z2 Pro',
    7 => 'ZUK Z2121',
    8 => 'ZUK Z2122',
    9 => 'ZUK Edge',
  ),
  '@ZV' => 
  array (
    0 => 'ZVII',
  ),
  '@ZX' => 
  array (
    0 => '(ZXY-)?(ZTE )?N[0-9]{4,4}!!',
    1 => '(ZXY-)?NX[0-9]{2,3}!!',
    2 => 'ZXY-ZTE-C X920',
    3 => 'ZXY-ZTE V6700',
  ),
);
